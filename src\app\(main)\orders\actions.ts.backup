
"use server";

import { revalidatePath } from 'next/cache';
import { z } from 'zod';
import { getFirebaseOrders, updateOrderStatusSchema } from './data';
import type { Order, OrderStatus } from '@/lib/types';
import { verifyIdTokenAndCheckAdmin, adminDb, Timestamp, FieldValue } from '@/lib/firebase-admin';
import type { Timestamp as TimestampType } from 'firebase-admin/firestore';
import { BalanceManager } from '@/lib/balance-system';

export async function fetchOrders(): Promise<Order[]> {
  return getFirebaseOrders();
}

// Helper function to calculate commission from order total
function calculateCommission(orderTotal: number): number {
  // Default commission rate: 10% - adjust this based on your business model
  const COMMISSION_RATE = 0.10;
  return orderTotal * COMMISSION_RATE;
}

// Helper function to update order status using Firebase Admin SDK
async function updateFirebaseOrderStatus(orderId: string, newStatus: OrderStatus): Promise<Order | null> {
  if (!adminDb) {
    console.error("CRITICAL: Firestore Admin DB instance is not available in updateFirebaseOrderStatus.");
    return null;
  }
  try {
    const orderRef = adminDb.collection('orders').doc(orderId);
    const docSnap = await orderRef.get();
    if (!docSnap.exists) {
      console.error(`Admin Action: Order with ID ${orderId} not found for status update.`);
      return null;
    }

    const orderData = docSnap.data();
    const oldStatus = orderData?.status;

    await orderRef.update({
      status: newStatus,
      updatedAt: FieldValue.serverTimestamp(),
    });

    const updatedDocSnap = await orderRef.get();
    if (updatedDocSnap.exists) {
      const data = updatedDocSnap.data();
      if (!data) return null;

      const order: Order = {
        id: updatedDocSnap.id,
        customerName: data.customerName,
        customerPhoneNumber: data.customerPhoneNumber,
        customerCity: data.customerCity,
        orderDate: (data.orderDate as TimestampType)?.toDate() || new Date(),
        totalAmount: data.totalAmount || 0,
        status: data.status as OrderStatus,
        items: data.items || [],
        shippingAddress: data.shippingAddress || '',
        notes: data.notes,
        createdAt: (data.createdAt as TimestampType)?.toDate() || new Date(),
        updatedAt: (data.updatedAt as TimestampType)?.toDate() || new Date(),
        userId: data.userId || '',
      } as Order;

      // NEW: Add earnings to user balance when order is marked as delivered
      if (newStatus === 'Delivered' && oldStatus !== 'Delivered' && order.userId) {
        try {
          const commission = calculateCommission(order.totalAmount);
          const success = await BalanceManager.addEarnings(
            order.userId,
            commission,
            order.id,
            `Commission from delivered order #${order.id.replace('order_', '')}`
          );

          if (success) {
            console.log(`✅ Added $${commission.toFixed(2)} commission to user ${order.userId} for delivered order ${order.id}`);
          } else {
            console.error(`❌ Failed to add commission for delivered order ${order.id}`);
          }
        } catch (error) {
          console.error(`❌ Error adding commission for delivered order ${order.id}:`, error);
        }
      }

      return order;
    }
    return null;
  } catch (error) {
    console.error(`Error updating order ${orderId} status in Firestore (Admin SDK):`, error);
    return null;
  }
}

// Helper function to delete an order using Firebase Admin SDK
async function deleteFirebaseOrder(orderId: string): Promise<boolean> {
  if (!adminDb) {
    console.error("CRITICAL: Firestore Admin DB instance is not available in deleteFirebaseOrder.");
    return false;
  }
  try {
    const orderRef = adminDb.collection('orders').doc(orderId);
    await orderRef.delete();
    return true;
  } catch (error) {
    console.error(`Error deleting order ${orderId} from Firestore (Admin SDK):`, error);
    return false;
  }
}


export async function updateOrderStatusAction(idToken: string | undefined, orderId: string, newStatus: OrderStatus) {
  try {
    if (!idToken) {
      return { success: false, error: 'Authentication token missing. Cannot perform action.' };
    }
    const adminUser = await verifyIdTokenAndCheckAdmin(idToken);
    if (!adminUser) {
      return { success: false, error: 'Admin verification failed. You do not have permission to perform this action.' };
    }

    const validatedData = updateOrderStatusSchema.parse({ status: newStatus });
    const updatedOrder = await updateFirebaseOrderStatus(orderId, validatedData.status);

    if (!updatedOrder) {
      return { success: false, error: 'Order not found or failed to update status. Check server logs.' };
    }
    revalidatePath('/orders');
    return { success: true, order: updatedOrder };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, errors: error.flatten().fieldErrors };
    }
    console.error("Error in updateOrderStatusAction (server action level):", error);
    return { success: false, error: 'An unexpected error occurred while updating order status.' };
  }
}

export async function deleteOrderAction(idToken: string | undefined, orderId: string) {
  try {
     if (!idToken) {
      return { success: false, error: 'Authentication token missing. Cannot perform delete action.' };
    }
    const adminUser = await verifyIdTokenAndCheckAdmin(idToken);
    if (!adminUser) {
      return { success: false, error: 'Admin verification failed. You do not have permission to perform this action.' };
    }

    const success = await deleteFirebaseOrder(orderId);
    if (!success) {
      return { success: false, error: 'Failed to delete order from database. Order might not exist or a server-side error occurred. Check server logs.' };
    }
    revalidatePath('/orders');
    return { success: true };
  } catch (error) {
    console.error("Error in deleteOrderAction (server action level):", error);
    return { success: false, error: 'An unexpected error occurred while deleting the order.' };
  }
}
