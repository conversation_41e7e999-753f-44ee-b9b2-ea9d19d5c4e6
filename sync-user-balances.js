// Migration Script: Sync User Balances Between Admin Panel and Flutter App
// This script ensures both collections have consistent data

const admin = require('firebase-admin');

// Initialize Firebase Admin (if not already initialized)
if (!admin.apps.length) {
  const serviceAccount = require('./dropshippingdinal-vq5iag-firebase-adminsdk-fbsvc-c5f412b27c.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    databaseURL: 'https://dropshippingdinal-vq5iag-default-rtdb.firebaseio.com'
  });
}

const db = admin.firestore();

async function syncUserBalances() {
  console.log(' Starting user balance synchronization...');
  
  try {
    // Get all users from the users collection (admin panel format)
    const usersSnapshot = await db.collection('users').get();
    console.log( Found  users in admin panel collection);

    let syncedCount = 0;
    let createdCount = 0;
    let errorCount = 0;

    for (const userDoc of usersSnapshot.docs) {
      try {
        const userId = userDoc.id;
        const userData = userDoc.data();
        
        console.log( Processing user: );

        // Get current balance data from admin panel
        const currentBalance = userData.currentBalance || 0;
        const totalEarnings = userData.totalEarnings || 0;
        const pendingWithdrawals = userData.pendingWithdrawals || 0;

        // Check if user has userBalances record (Flutter app format)
        const userBalanceRef = db.collection('userBalances').doc(userId);
        const userBalanceDoc = await userBalanceRef.get();

        if (userBalanceDoc.exists) {
          // Update existing userBalances record
          const existingBalance = userBalanceDoc.data();
          
          await userBalanceRef.update({
            availableBalance: currentBalance,
            totalEarnings: totalEarnings,
            pendingWithdrawals: pendingWithdrawals,
            incomingEarnings: existingBalance.incomingEarnings || 0, // Keep existing incoming
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            syncedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          
          console.log( Updated userBalances for : available="use server";

import { revalidatePath } from 'next/cache';
import { z } from 'zod';
import { getFirebaseOrders, updateOrderStatusSchema } from './data';
import type { Order, OrderStatus } from '@/lib/types';
import { verifyIdTokenAndCheckAdmin, adminDb, Timestamp, FieldValue } from '@/lib/firebase-admin';
import type { Timestamp as TimestampType } from 'firebase-admin/firestore';

export async function fetchOrders(): Promise<Order[]> {
  return getFirebaseOrders();
}

// Helper function to call the unified earnings API
async function processEarningsViaAPI(userId: string, orderId: string, status: string, totalAmount: number): Promise<boolean> {
  try {
    const response = await fetch(${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/flutter/earnings, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId: userId,
        orderId: orderId,
        status: status,
        totalAmount: totalAmount
      }),
    });

    const result = await response.json();
    
    if (result.success) {
      console.log( Earnings processed via API: $${result.earningsAmount?.toFixed(2)} for user ${userId});
      return true;
    } else {
      console.error( API earnings processing failed: ${result.error});
      return false;
    }
  } catch (error) {
    console.error( Error calling earnings API:, error);
    return false;
  }
}

// Helper function to update order status using Firebase Admin SDK
async function updateFirebaseOrderStatus(orderId: string, newStatus: OrderStatus): Promise<Order | null> {
  if (!adminDb) {
    console.error("CRITICAL: Firestore Admin DB instance is not available in updateFirebaseOrderStatus.");
    return null;
  }
  try {
    const orderRef = adminDb.collection('orders').doc(orderId);
    const docSnap = await orderRef.get();
    if (!docSnap.exists) {
      console.error(Admin Action: Order with ID ${orderId} not found for status update.);
      return null;
    }

    const orderData = docSnap.data();
    const oldStatus = orderData?.status;

    await orderRef.update({
      status: newStatus,
      updatedAt: FieldValue.serverTimestamp(),
    });

    const updatedDocSnap = await orderRef.get();
    if (updatedDocSnap.exists) {
      const data = updatedDocSnap.data();
      if (!data) return null;

      const order: Order = {
        id: updatedDocSnap.id,
        customerName: data.customerName,
        customerPhoneNumber: data.customerPhoneNumber,
        customerCity: data.customerCity,
        orderDate: (data.orderDate as TimestampType)?.toDate() || new Date(),
        totalAmount: data.totalAmount || 0,
        status: data.status as OrderStatus,
        items: data.items || [],
        shippingAddress: data.shippingAddress || '',
        notes: data.notes,
        createdAt: (data.createdAt as TimestampType)?.toDate() || new Date(),
        updatedAt: (data.updatedAt as TimestampType)?.toDate() || new Date(),
        userId: data.userId || '',
      } as Order;

      // NEW: Use unified earnings API when order is marked as delivered
      if (newStatus === 'Delivered' && oldStatus !== 'Delivered' && order.userId) {
        try {
          console.log( Processing earnings for delivered order ${order.id}...);
          const success = await processEarningsViaAPI(
            order.userId,
            order.id,
            'delivered',
            order.totalAmount
          );

          if (success) {
            console.log( Earnings successfully processed for delivered order ${order.id});
          } else {
            console.error( Failed to process earnings for delivered order ${order.id});
          }
        } catch (error) {
          console.error( Error processing earnings for delivered order ${order.id}:, error);
        }
      }

      return order;
    }
    return null;
  } catch (error) {
    console.error(Error updating order ${orderId} status in Firestore (Admin SDK):, error);
    return null;
  }
}

// Helper function to delete an order using Firebase Admin SDK
async function deleteFirebaseOrder(orderId: string): Promise<boolean> {
  if (!adminDb) {
    console.error("CRITICAL: Firestore Admin DB instance is not available in deleteFirebaseOrder.");
    return false;
  }
  try {
    const orderRef = adminDb.collection('orders').doc(orderId);
    await orderRef.delete();
    return true;
  } catch (error) {
    console.error(Error deleting order ${orderId} from Firestore (Admin SDK):, error);
    return false;
  }
}

export async function updateOrderStatusAction(idToken: string | undefined, orderId: string, newStatus: OrderStatus) {
  try {
    if (!idToken) {
      return { success: false, error: 'Authentication token missing. Cannot perform action.' };
    }
    const adminUser = await verifyIdTokenAndCheckAdmin(idToken);
    if (!adminUser) {
      return { success: false, error: 'Admin verification failed. You do not have permission to perform this action.' };
    }

    const validatedData = updateOrderStatusSchema.parse({ status: newStatus });
    const updatedOrder = await updateFirebaseOrderStatus(orderId, validatedData.status);

    if (!updatedOrder) {
      return { success: false, error: 'Order not found or failed to update status. Check server logs.' };
    }
    revalidatePath('/orders');
    return { success: true, order: updatedOrder };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, errors: error.flatten().fieldErrors };
    }
    console.error("Error in updateOrderStatusAction (server action level):", error);
    return { success: false, error: 'An unexpected error occurred while updating order status.' };
  }
}

export async function deleteOrderAction(idToken: string | undefined, orderId: string) {
  try {
     if (!idToken) {
      return { success: false, error: 'Authentication token missing. Cannot perform delete action.' };
    }
    const adminUser = await verifyIdTokenAndCheckAdmin(idToken);
    if (!adminUser) {
      return { success: false, error: 'Admin verification failed. You do not have permission to perform this action.' };
    }

    const success = await deleteFirebaseOrder(orderId);
    if (!success) {
      return { success: false, error: 'Failed to delete order from database. Order might not exist or a server-side error occurred. Check server logs.' };
    }
    revalidatePath('/orders');
    return { success: true };
  } catch (error) {
    console.error("Error in deleteOrderAction (server action level):", error);
    return { success: false, error: 'An unexpected error occurred while deleting the order.' };
  }
}{currentBalance});
          syncedCount++;
        } else {
          // Create new userBalances record
          await userBalanceRef.set({
            userId: userId,
            availableBalance: currentBalance,
            incomingEarnings: 0, // Start with 0 incoming earnings
            totalEarnings: totalEarnings,
            pendingWithdrawals: pendingWithdrawals,
            totalWithdrawn: 0,
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            syncedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          
          console.log( Created userBalances for : available="use server";

import { revalidatePath } from 'next/cache';
import { z } from 'zod';
import { getFirebaseOrders, updateOrderStatusSchema } from './data';
import type { Order, OrderStatus } from '@/lib/types';
import { verifyIdTokenAndCheckAdmin, adminDb, Timestamp, FieldValue } from '@/lib/firebase-admin';
import type { Timestamp as TimestampType } from 'firebase-admin/firestore';

export async function fetchOrders(): Promise<Order[]> {
  return getFirebaseOrders();
}

// Helper function to call the unified earnings API
async function processEarningsViaAPI(userId: string, orderId: string, status: string, totalAmount: number): Promise<boolean> {
  try {
    const response = await fetch(${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/flutter/earnings, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId: userId,
        orderId: orderId,
        status: status,
        totalAmount: totalAmount
      }),
    });

    const result = await response.json();
    
    if (result.success) {
      console.log( Earnings processed via API: $${result.earningsAmount?.toFixed(2)} for user ${userId});
      return true;
    } else {
      console.error( API earnings processing failed: ${result.error});
      return false;
    }
  } catch (error) {
    console.error( Error calling earnings API:, error);
    return false;
  }
}

// Helper function to update order status using Firebase Admin SDK
async function updateFirebaseOrderStatus(orderId: string, newStatus: OrderStatus): Promise<Order | null> {
  if (!adminDb) {
    console.error("CRITICAL: Firestore Admin DB instance is not available in updateFirebaseOrderStatus.");
    return null;
  }
  try {
    const orderRef = adminDb.collection('orders').doc(orderId);
    const docSnap = await orderRef.get();
    if (!docSnap.exists) {
      console.error(Admin Action: Order with ID ${orderId} not found for status update.);
      return null;
    }

    const orderData = docSnap.data();
    const oldStatus = orderData?.status;

    await orderRef.update({
      status: newStatus,
      updatedAt: FieldValue.serverTimestamp(),
    });

    const updatedDocSnap = await orderRef.get();
    if (updatedDocSnap.exists) {
      const data = updatedDocSnap.data();
      if (!data) return null;

      const order: Order = {
        id: updatedDocSnap.id,
        customerName: data.customerName,
        customerPhoneNumber: data.customerPhoneNumber,
        customerCity: data.customerCity,
        orderDate: (data.orderDate as TimestampType)?.toDate() || new Date(),
        totalAmount: data.totalAmount || 0,
        status: data.status as OrderStatus,
        items: data.items || [],
        shippingAddress: data.shippingAddress || '',
        notes: data.notes,
        createdAt: (data.createdAt as TimestampType)?.toDate() || new Date(),
        updatedAt: (data.updatedAt as TimestampType)?.toDate() || new Date(),
        userId: data.userId || '',
      } as Order;

      // NEW: Use unified earnings API when order is marked as delivered
      if (newStatus === 'Delivered' && oldStatus !== 'Delivered' && order.userId) {
        try {
          console.log( Processing earnings for delivered order ${order.id}...);
          const success = await processEarningsViaAPI(
            order.userId,
            order.id,
            'delivered',
            order.totalAmount
          );

          if (success) {
            console.log( Earnings successfully processed for delivered order ${order.id});
          } else {
            console.error( Failed to process earnings for delivered order ${order.id});
          }
        } catch (error) {
          console.error( Error processing earnings for delivered order ${order.id}:, error);
        }
      }

      return order;
    }
    return null;
  } catch (error) {
    console.error(Error updating order ${orderId} status in Firestore (Admin SDK):, error);
    return null;
  }
}

// Helper function to delete an order using Firebase Admin SDK
async function deleteFirebaseOrder(orderId: string): Promise<boolean> {
  if (!adminDb) {
    console.error("CRITICAL: Firestore Admin DB instance is not available in deleteFirebaseOrder.");
    return false;
  }
  try {
    const orderRef = adminDb.collection('orders').doc(orderId);
    await orderRef.delete();
    return true;
  } catch (error) {
    console.error(Error deleting order ${orderId} from Firestore (Admin SDK):, error);
    return false;
  }
}

export async function updateOrderStatusAction(idToken: string | undefined, orderId: string, newStatus: OrderStatus) {
  try {
    if (!idToken) {
      return { success: false, error: 'Authentication token missing. Cannot perform action.' };
    }
    const adminUser = await verifyIdTokenAndCheckAdmin(idToken);
    if (!adminUser) {
      return { success: false, error: 'Admin verification failed. You do not have permission to perform this action.' };
    }

    const validatedData = updateOrderStatusSchema.parse({ status: newStatus });
    const updatedOrder = await updateFirebaseOrderStatus(orderId, validatedData.status);

    if (!updatedOrder) {
      return { success: false, error: 'Order not found or failed to update status. Check server logs.' };
    }
    revalidatePath('/orders');
    return { success: true, order: updatedOrder };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, errors: error.flatten().fieldErrors };
    }
    console.error("Error in updateOrderStatusAction (server action level):", error);
    return { success: false, error: 'An unexpected error occurred while updating order status.' };
  }
}

export async function deleteOrderAction(idToken: string | undefined, orderId: string) {
  try {
     if (!idToken) {
      return { success: false, error: 'Authentication token missing. Cannot perform delete action.' };
    }
    const adminUser = await verifyIdTokenAndCheckAdmin(idToken);
    if (!adminUser) {
      return { success: false, error: 'Admin verification failed. You do not have permission to perform this action.' };
    }

    const success = await deleteFirebaseOrder(orderId);
    if (!success) {
      return { success: false, error: 'Failed to delete order from database. Order might not exist or a server-side error occurred. Check server logs.' };
    }
    revalidatePath('/orders');
    return { success: true };
  } catch (error) {
    console.error("Error in deleteOrderAction (server action level):", error);
    return { success: false, error: 'An unexpected error occurred while deleting the order.' };
  }
}{currentBalance});
          createdCount++;
        }

        // Also ensure users collection has all required fields
        await userDoc.ref.update({
          lastActivity: userData.lastActivity || admin.firestore.FieldValue.serverTimestamp(),
          syncedAt: admin.firestore.FieldValue.serverTimestamp(),
        });

      } catch (error) {
        console.error( Error processing user :, error);
        errorCount++;
      }
    }

    console.log(' Synchronization Summary:');
    console.log(    Updated:  users);
    console.log(    Created:  users);
    console.log(    Errors:  users);
    console.log(' User balance synchronization completed!');

  } catch (error) {
    console.error(' Error during synchronization:', error);
  }
}

async function syncOrderEarnings() {
  console.log(' Starting order earnings synchronization...');
  
  try {
    // Get all orders that are delivered but don't have earnings confirmed
    const ordersSnapshot = await db.collection('orders')
      .where('status', '==', 'Delivered')
      .get();
    
    console.log( Found  delivered orders);

    let processedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    for (const orderDoc of ordersSnapshot.docs) {
      try {
        const orderId = orderDoc.id;
        const orderData = orderDoc.data();
        const userId = orderData.userId;
        const totalAmount = orderData.totalAmount || 0;
        const earningsConfirmed = orderData.earningsConfirmed || false;

        if (!userId) {
          console.log( Skipping order : no userId);
          skippedCount++;
          continue;
        }

        if (earningsConfirmed) {
          console.log( Skipping order : earnings already confirmed);
          skippedCount++;
          continue;
        }

        // Calculate commission (10%)
        const commission = totalAmount * 0.10;

        if (commission <= 0) {
          console.log( Skipping order : no commission to process);
          skippedCount++;
          continue;
        }

        console.log( Processing earnings for order : "use server";

import { revalidatePath } from 'next/cache';
import { z } from 'zod';
import { getFirebaseOrders, updateOrderStatusSchema } from './data';
import type { Order, OrderStatus } from '@/lib/types';
import { verifyIdTokenAndCheckAdmin, adminDb, Timestamp, FieldValue } from '@/lib/firebase-admin';
import type { Timestamp as TimestampType } from 'firebase-admin/firestore';

export async function fetchOrders(): Promise<Order[]> {
  return getFirebaseOrders();
}

// Helper function to call the unified earnings API
async function processEarningsViaAPI(userId: string, orderId: string, status: string, totalAmount: number): Promise<boolean> {
  try {
    const response = await fetch(${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/flutter/earnings, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId: userId,
        orderId: orderId,
        status: status,
        totalAmount: totalAmount
      }),
    });

    const result = await response.json();
    
    if (result.success) {
      console.log( Earnings processed via API: $${result.earningsAmount?.toFixed(2)} for user ${userId});
      return true;
    } else {
      console.error( API earnings processing failed: ${result.error});
      return false;
    }
  } catch (error) {
    console.error( Error calling earnings API:, error);
    return false;
  }
}

// Helper function to update order status using Firebase Admin SDK
async function updateFirebaseOrderStatus(orderId: string, newStatus: OrderStatus): Promise<Order | null> {
  if (!adminDb) {
    console.error("CRITICAL: Firestore Admin DB instance is not available in updateFirebaseOrderStatus.");
    return null;
  }
  try {
    const orderRef = adminDb.collection('orders').doc(orderId);
    const docSnap = await orderRef.get();
    if (!docSnap.exists) {
      console.error(Admin Action: Order with ID ${orderId} not found for status update.);
      return null;
    }

    const orderData = docSnap.data();
    const oldStatus = orderData?.status;

    await orderRef.update({
      status: newStatus,
      updatedAt: FieldValue.serverTimestamp(),
    });

    const updatedDocSnap = await orderRef.get();
    if (updatedDocSnap.exists) {
      const data = updatedDocSnap.data();
      if (!data) return null;

      const order: Order = {
        id: updatedDocSnap.id,
        customerName: data.customerName,
        customerPhoneNumber: data.customerPhoneNumber,
        customerCity: data.customerCity,
        orderDate: (data.orderDate as TimestampType)?.toDate() || new Date(),
        totalAmount: data.totalAmount || 0,
        status: data.status as OrderStatus,
        items: data.items || [],
        shippingAddress: data.shippingAddress || '',
        notes: data.notes,
        createdAt: (data.createdAt as TimestampType)?.toDate() || new Date(),
        updatedAt: (data.updatedAt as TimestampType)?.toDate() || new Date(),
        userId: data.userId || '',
      } as Order;

      // NEW: Use unified earnings API when order is marked as delivered
      if (newStatus === 'Delivered' && oldStatus !== 'Delivered' && order.userId) {
        try {
          console.log( Processing earnings for delivered order ${order.id}...);
          const success = await processEarningsViaAPI(
            order.userId,
            order.id,
            'delivered',
            order.totalAmount
          );

          if (success) {
            console.log( Earnings successfully processed for delivered order ${order.id});
          } else {
            console.error( Failed to process earnings for delivered order ${order.id});
          }
        } catch (error) {
          console.error( Error processing earnings for delivered order ${order.id}:, error);
        }
      }

      return order;
    }
    return null;
  } catch (error) {
    console.error(Error updating order ${orderId} status in Firestore (Admin SDK):, error);
    return null;
  }
}

// Helper function to delete an order using Firebase Admin SDK
async function deleteFirebaseOrder(orderId: string): Promise<boolean> {
  if (!adminDb) {
    console.error("CRITICAL: Firestore Admin DB instance is not available in deleteFirebaseOrder.");
    return false;
  }
  try {
    const orderRef = adminDb.collection('orders').doc(orderId);
    await orderRef.delete();
    return true;
  } catch (error) {
    console.error(Error deleting order ${orderId} from Firestore (Admin SDK):, error);
    return false;
  }
}

export async function updateOrderStatusAction(idToken: string | undefined, orderId: string, newStatus: OrderStatus) {
  try {
    if (!idToken) {
      return { success: false, error: 'Authentication token missing. Cannot perform action.' };
    }
    const adminUser = await verifyIdTokenAndCheckAdmin(idToken);
    if (!adminUser) {
      return { success: false, error: 'Admin verification failed. You do not have permission to perform this action.' };
    }

    const validatedData = updateOrderStatusSchema.parse({ status: newStatus });
    const updatedOrder = await updateFirebaseOrderStatus(orderId, validatedData.status);

    if (!updatedOrder) {
      return { success: false, error: 'Order not found or failed to update status. Check server logs.' };
    }
    revalidatePath('/orders');
    return { success: true, order: updatedOrder };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, errors: error.flatten().fieldErrors };
    }
    console.error("Error in updateOrderStatusAction (server action level):", error);
    return { success: false, error: 'An unexpected error occurred while updating order status.' };
  }
}

export async function deleteOrderAction(idToken: string | undefined, orderId: string) {
  try {
     if (!idToken) {
      return { success: false, error: 'Authentication token missing. Cannot perform delete action.' };
    }
    const adminUser = await verifyIdTokenAndCheckAdmin(idToken);
    if (!adminUser) {
      return { success: false, error: 'Admin verification failed. You do not have permission to perform this action.' };
    }

    const success = await deleteFirebaseOrder(orderId);
    if (!success) {
      return { success: false, error: 'Failed to delete order from database. Order might not exist or a server-side error occurred. Check server logs.' };
    }
    revalidatePath('/orders');
    return { success: true };
  } catch (error) {
    console.error("Error in deleteOrderAction (server action level):", error);
    return { success: false, error: 'An unexpected error occurred while deleting the order.' };
  }
}{commission.toFixed(2)});

        // Create or update earnings record
        const earningsQuery = await db.collection('earnings')
          .where('orderId', '==', orderId)
          .where('userId', '==', userId)
          .get();

        if (earningsQuery.empty) {
          // Create earnings record
          await db.collection('earnings').add({
            userId: userId,
            orderId: orderId,
            amount: commission,
            status: 'confirmed',
            orderStatus: 'delivered',
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            confirmedAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          console.log( Created earnings record for order );
        }

        // Mark order earnings as confirmed
        await orderDoc.ref.update({
          earningsConfirmed: true,
          earningsConfirmedAt: admin.firestore.FieldValue.serverTimestamp(),
          updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        });

        processedCount++;

      } catch (error) {
        console.error( Error processing order :, error);
        errorCount++;
      }
    }

    console.log(' Order Earnings Sync Summary:');
    console.log(    Processed:  orders);
    console.log(    Skipped:  orders);
    console.log(    Errors:  orders);
    console.log(' Order earnings synchronization completed!');

  } catch (error) {
    console.error(' Error during order earnings sync:', error);
  }
}

// Main execution
async function main() {
  console.log(' Starting complete balance synchronization...');
  console.log('=====================================');
  
  await syncUserBalances();
  console.log('');
  await syncOrderEarnings();
  
  console.log('');
  console.log(' Complete synchronization finished!');
  console.log('Your admin panel and Flutter app should now have consistent balance data.');
  
  process.exit(0);
}

// Run the migration
main().catch(console.error);
