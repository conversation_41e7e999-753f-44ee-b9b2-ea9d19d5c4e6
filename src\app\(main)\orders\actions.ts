"use server";

import { revalidatePath } from 'next/cache';
import { z } from 'zod';
import { getFirebaseOrders, updateOrderStatusSchema } from './data';
import type { Order, OrderStatus } from '@/lib/types';
import { verifyIdTokenAndCheckAdmin, adminDb, Timestamp, FieldValue } from '@/lib/firebase-admin';
import type { Timestamp as TimestampType } from 'firebase-admin/firestore';

export async function fetchOrders(): Promise<Order[]> {
  return getFirebaseOrders();
}

// Helper function to call the unified earnings API
async function processEarningsViaAPI(userId: string, orderId: string, status: string, totalAmount: number): Promise<boolean> {
  try {
    const response = await fetch(${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/flutter/earnings, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId: userId,
        orderId: orderId,
        status: status,
        totalAmount: totalAmount
      }),
    });

    const result = await response.json();
    
    if (result.success) {
      console.log( Earnings processed via API: C:\Users\<USER>\OneDrive\Desktop\admin panel final\src\app\(main)\orders\actions.ts.backup{result.earningsAmount?.toFixed(2)} for user );
      return true;
    } else {
      console.error( API earnings processing failed: );
      return false;
    }
  } catch (error) {
    console.error( Error calling earnings API:, error);
    return false;
  }
}

// Helper function to update order status using Firebase Admin SDK
async function updateFirebaseOrderStatus(orderId: string, newStatus: OrderStatus): Promise<Order | null> {
  if (!adminDb) {
    console.error("CRITICAL: Firestore Admin DB instance is not available in updateFirebaseOrderStatus.");
    return null;
  }
  try {
    const orderRef = adminDb.collection('orders').doc(orderId);
    const docSnap = await orderRef.get();
    if (!docSnap.exists) {
      console.error(Admin Action: Order with ID  not found for status update.);
      return null;
    }

    const orderData = docSnap.data();
    const oldStatus = orderData?.status;

    await orderRef.update({
      status: newStatus,
      updatedAt: FieldValue.serverTimestamp(),
    });

    const updatedDocSnap = await orderRef.get();
    if (updatedDocSnap.exists) {
      const data = updatedDocSnap.data();
      if (!data) return null;

      const order: Order = {
        id: updatedDocSnap.id,
        customerName: data.customerName,
        customerPhoneNumber: data.customerPhoneNumber,
        customerCity: data.customerCity,
        orderDate: (data.orderDate as TimestampType)?.toDate() || new Date(),
        totalAmount: data.totalAmount || 0,
        status: data.status as OrderStatus,
        items: data.items || [],
        shippingAddress: data.shippingAddress || '',
        notes: data.notes,
        createdAt: (data.createdAt as TimestampType)?.toDate() || new Date(),
        updatedAt: (data.updatedAt as TimestampType)?.toDate() || new Date(),
        userId: data.userId || '',
      } as Order;

      // NEW: Use unified earnings API when order is marked as delivered
      if (newStatus === 'Delivered' && oldStatus !== 'Delivered' && order.userId) {
        try {
          console.log( Processing earnings for delivered order ...);
          const success = await processEarningsViaAPI(
            order.userId,
            order.id,
            'delivered',
            order.totalAmount
          );

          if (success) {
            console.log( Earnings successfully processed for delivered order );
          } else {
            console.error( Failed to process earnings for delivered order );
          }
        } catch (error) {
          console.error( Error processing earnings for delivered order :, error);
        }
      }

      return order;
    }
    return null;
  } catch (error) {
    console.error(Error updating order  status in Firestore (Admin SDK):, error);
    return null;
  }
}

// Helper function to delete an order using Firebase Admin SDK
async function deleteFirebaseOrder(orderId: string): Promise<boolean> {
  if (!adminDb) {
    console.error("CRITICAL: Firestore Admin DB instance is not available in deleteFirebaseOrder.");
    return false;
  }
  try {
    const orderRef = adminDb.collection('orders').doc(orderId);
    await orderRef.delete();
    return true;
  } catch (error) {
    console.error(Error deleting order  from Firestore (Admin SDK):, error);
    return false;
  }
}

export async function updateOrderStatusAction(idToken: string | undefined, orderId: string, newStatus: OrderStatus) {
  try {
    if (!idToken) {
      return { success: false, error: 'Authentication token missing. Cannot perform action.' };
    }
    const adminUser = await verifyIdTokenAndCheckAdmin(idToken);
    if (!adminUser) {
      return { success: false, error: 'Admin verification failed. You do not have permission to perform this action.' };
    }

    const validatedData = updateOrderStatusSchema.parse({ status: newStatus });
    const updatedOrder = await updateFirebaseOrderStatus(orderId, validatedData.status);

    if (!updatedOrder) {
      return { success: false, error: 'Order not found or failed to update status. Check server logs.' };
    }
    revalidatePath('/orders');
    return { success: true, order: updatedOrder };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, errors: error.flatten().fieldErrors };
    }
    console.error("Error in updateOrderStatusAction (server action level):", error);
    return { success: false, error: 'An unexpected error occurred while updating order status.' };
  }
}

export async function deleteOrderAction(idToken: string | undefined, orderId: string) {
  try {
     if (!idToken) {
      return { success: false, error: 'Authentication token missing. Cannot perform delete action.' };
    }
    const adminUser = await verifyIdTokenAndCheckAdmin(idToken);
    if (!adminUser) {
      return { success: false, error: 'Admin verification failed. You do not have permission to perform this action.' };
    }

    const success = await deleteFirebaseOrder(orderId);
    if (!success) {
      return { success: false, error: 'Failed to delete order from database. Order might not exist or a server-side error occurred. Check server logs.' };
    }
    revalidatePath('/orders');
    return { success: true };
  } catch (error) {
    console.error("Error in deleteOrderAction (server action level):", error);
    return { success: false, error: 'An unexpected error occurred while deleting the order.' };
  }
}
