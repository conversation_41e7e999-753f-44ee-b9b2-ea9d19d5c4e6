import { NextRequest, NextResponse } from 'next/server';
import { adminAuth, adminDb, FieldValue } from '@/lib/firebase-admin';

/**
 * Unified Earnings API Endpoint
 * This endpoint handles earnings transfer in a way that's compatible with both
 * the admin panel and Flutter app expectations.
 */

// Helper function to calculate commission from order total
function calculateCommission(orderTotal: number): number {
  const COMMISSION_RATE = 0.10; // 10% commission rate
  return orderTotal * COMMISSION_RATE;
}

/**
 * Transfer earnings from incoming to available balance (Flutter app compatible)
 * This function ensures earnings are properly moved when orders are delivered
 */
async function transferEarningsToAvailable(
  userId: string, 
  orderId: string, 
  earningsAmount: number
): Promise<boolean> {
  try {
    if (!adminDb) {
      console.error('Firebase Admin DB not available');
      return false;
    }

    await adminDb.runTransaction(async (transaction) => {
      // Update the order to mark earnings as confirmed
      const orderRef = adminDb.collection('orders').doc(orderId);
      transaction.update(orderRef, {
        earningsConfirmed: true,
        earningsConfirmedAt: FieldValue.serverTimestamp(),
        updatedAt: FieldValue.serverTimestamp(),
      });

      // Update userBalances collection (Flutter app format)
      const userBalanceRef = adminDb.collection('userBalances').doc(userId);
      transaction.set(userBalanceRef, {
        userId: userId,
        availableBalance: FieldValue.increment(earningsAmount),
        incomingEarnings: FieldValue.increment(-earningsAmount),
        totalEarnings: FieldValue.increment(0), // No change to total
        updatedAt: FieldValue.serverTimestamp(),
      }, { merge: true });

      // Update earnings collection
      const earningsQuery = await adminDb.collection('earnings')
        .where('orderId', '==', orderId)
        .where('userId', '==', userId)
        .get();

      if (!earningsQuery.empty) {
        const earningsRef = earningsQuery.docs[0].ref;
        transaction.update(earningsRef, {
          status: 'confirmed',
          orderStatus: 'delivered',
          confirmedAt: FieldValue.serverTimestamp(),
          updatedAt: FieldValue.serverTimestamp(),
        });
      } else {
        // Create earnings record if it doesn't exist
        const newEarningsRef = adminDb.collection('earnings').doc();
        transaction.set(newEarningsRef, {
          userId: userId,
          orderId: orderId,
          amount: earningsAmount,
          status: 'confirmed',
          orderStatus: 'delivered',
          createdAt: FieldValue.serverTimestamp(),
          confirmedAt: FieldValue.serverTimestamp(),
          updatedAt: FieldValue.serverTimestamp(),
        });
      }

      // Also update users collection for admin panel compatibility
      const userRef = adminDb.collection('users').doc(userId);
      transaction.set(userRef, {
        currentBalance: FieldValue.increment(earningsAmount),
        totalEarnings: FieldValue.increment(earningsAmount),
        lastActivity: FieldValue.serverTimestamp(),
      }, { merge: true });

      // Create transaction record
      const transactionRef = adminDb.collection('transactions').doc();
      transaction.set(transactionRef, {
        id: transactionRef.id,
        userId: userId,
        type: 'earning',
        amount: earningsAmount,
        description: Commission from delivered order #,
        orderId: orderId,
        createdAt: FieldValue.serverTimestamp(),
        status: 'completed'
      });
    });

    console.log( Successfully transferred -Force{earningsAmount.toFixed(2)} earnings to available balance for user );
    return true;
  } catch (error) {
    console.error(' Error transferring earnings to available balance:', error);
    return false;
  }
}

/**
 * PUT /api/flutter/earnings - Update order earnings when marked as delivered
 */
export async function PUT(request: NextRequest) {
  try {
    const { orderId, userId, status, totalAmount } = await request.json();

    if (!orderId || !userId || !status) {
      return NextResponse.json(
        { success: false, error: 'Order ID, User ID, and status are required' },
        { status: 400 }
      );
    }

    // Only process when order is marked as delivered
    if (status !== 'delivered' && status !== 'Delivered') {
      return NextResponse.json(
        { success: false, error: 'This endpoint only processes delivered orders' },
        { status: 400 }
      );
    }

    // Get the order to check current status and calculate earnings
    const orderDoc = await adminDb?.collection('orders').doc(orderId).get();
    
    if (!orderDoc?.exists) {
      return NextResponse.json(
        { success: false, error: 'Order not found' },
        { status: 404 }
      );
    }

    const orderData = orderDoc.data();
    const currentStatus = orderData?.status;
    const earningsConfirmed = orderData?.earningsConfirmed || false;
    const orderTotalAmount = totalAmount || orderData?.totalAmount || 0;

    // Don't process if earnings already confirmed
    if (earningsConfirmed) {
      return NextResponse.json({
        success: true,
        message: 'Earnings already confirmed for this order',
        alreadyProcessed: true
      });
    }

    // Calculate earnings
    const earningsAmount = calculateCommission(orderTotalAmount);

    if (earningsAmount <= 0) {
      return NextResponse.json(
        { success: false, error: 'Invalid earnings amount calculated' },
        { status: 400 }
      );
    }

    // Transfer earnings to available balance
    const success = await transferEarningsToAvailable(userId, orderId, earningsAmount);

    if (success) {
      return NextResponse.json({
        success: true,
        message: 'Earnings successfully transferred to available balance',
        earningsAmount: earningsAmount,
        orderId: orderId,
        userId: userId
      });
    } else {
      return NextResponse.json(
        { success: false, error: 'Failed to transfer earnings' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Flutter Earnings API Error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process earnings transfer',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/flutter/earnings - Get user earnings summary
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Get user balance from userBalances collection
    const userBalanceDoc = await adminDb?.collection('userBalances').doc(userId).get();
    const userBalance = userBalanceDoc?.data() || {};

    // Get recent earnings
    const earningsSnapshot = await adminDb?.collection('earnings')
      .where('userId', '==', userId)
      .orderBy('createdAt', 'desc')
      .limit(10)
      .get();

    const earnings = earningsSnapshot?.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || null,
      confirmedAt: doc.data().confirmedAt?.toDate?.()?.toISOString() || null,
    })) || [];

    return NextResponse.json({
      success: true,
      balance: {
        availableBalance: userBalance.availableBalance || 0,
        incomingEarnings: userBalance.incomingEarnings || 0,
        totalEarnings: userBalance.totalEarnings || 0,
      },
      recentEarnings: earnings
    });

  } catch (error) {
    console.error('Flutter Earnings GET API Error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch earnings data' },
      { status: 500 }
    );
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, PUT, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
