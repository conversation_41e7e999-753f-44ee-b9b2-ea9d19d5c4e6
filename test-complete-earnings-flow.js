// Complete Earnings Flow Test
// Tests: Order Creation  Admin Marks Delivered  Earnings in Flutter App

const admin = require('firebase-admin');

// Initialize Firebase Admin (if not already initialized)
if (!admin.apps.length) {
  const serviceAccount = require('./dropshippingdinal-vq5iag-firebase-adminsdk-fbsvc-c5f412b27c.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    databaseURL: 'https://dropshippingdinal-vq5iag-default-rtdb.firebaseio.com'
  });
}

const db = admin.firestore();

// Test user ID (you can change this to your actual user ID)
const TEST_USER_ID = 'test_user_earnings_flow';

async function createTestUser() {
  console.log(' Creating test user...');
  
  try {
    // Create user in users collection (admin panel format)
    await db.collection('users').doc(TEST_USER_ID).set({
      name: 'Test User',
      email: '<EMAIL>',
      phone: '+**********',
      currentBalance: 0,
      totalEarnings: 0,
      pendingWithdrawals: 0,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      lastActivity: admin.firestore.FieldValue.serverTimestamp(),
    });

    // Create user in userBalances collection (Flutter app format)
    await db.collection('userBalances').doc(TEST_USER_ID).set({
      userId: TEST_USER_ID,
      availableBalance: 0,
      incomingEarnings: 0,
      totalEarnings: 0,
      pendingWithdrawals: 0,
      totalWithdrawn: 0,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    console.log(' Test user created successfully');
    return true;
  } catch (error) {
    console.error(' Error creating test user:', error);
    return false;
  }
}

async function createTestOrder() {
  console.log(' Creating test order...');
  
  try {
    const orderRef = db.collection('orders').doc();
    const orderData = {
      id: orderRef.id,
      userId: TEST_USER_ID,
      customerName: 'Test Customer',
      customerPhoneNumber: '+**********',
      customerCity: 'Test City',
      shippingAddress: '123 Test Street',
      totalAmount: 100.0, //  order =  commission
      status: 'Pending',
      items: [
        {
          productId: 'test-product-1',
          productName: 'Test Product',
          quantity: 1,
          price: 100.0,
          imageUrl: 'https://example.com/image.jpg'
        }
      ],
      notes: 'Test order for earnings flow',
      orderDate: admin.firestore.FieldValue.serverTimestamp(),
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      earningsConfirmed: false,
    };

    await orderRef.set(orderData);

    // Add earnings to incoming balance (simulating order creation)
    await db.collection('userBalances').doc(TEST_USER_ID).update({
      incomingEarnings: admin.firestore.FieldValue.increment(10.0),
      totalEarnings: admin.firestore.FieldValue.increment(10.0),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    console.log( Test order created: );
    console.log(' Added  to incoming earnings');
    return orderRef.id;
  } catch (error) {
    console.error(' Error creating test order:', error);
    return null;
  }
}

async function simulateAdminDelivery(orderId) {
  console.log(' Simulating admin marking order as delivered...');
  
  try {
    // Call the unified earnings API
    const response = await fetch('http://localhost:3000/api/flutter/earnings', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId: TEST_USER_ID,
        orderId: orderId,
        status: 'delivered',
        totalAmount: 100.0
      }),
    });

    const result = await response.json();
    
    if (result.success) {
      console.log(' Earnings API call successful');
      console.log( Earnings amount: // Migration Script: Sync User Balances Between Admin Panel and Flutter App
// This script ensures both collections have consistent data

const admin = require('firebase-admin');

// Initialize Firebase Admin (if not already initialized)
if (!admin.apps.length) {
  const serviceAccount = require('./dropshippingdinal-vq5iag-firebase-adminsdk-fbsvc-c5f412b27c.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    databaseURL: 'https://dropshippingdinal-vq5iag-default-rtdb.firebaseio.com'
  });
}

const db = admin.firestore();

async function syncUserBalances() {
  console.log(' Starting user balance synchronization...');
  
  try {
    // Get all users from the users collection (admin panel format)
    const usersSnapshot = await db.collection('users').get();
    console.log( Found ${usersSnapshot.size} users in admin panel collection);

    let syncedCount = 0;
    let createdCount = 0;
    let errorCount = 0;

    for (const userDoc of usersSnapshot.docs) {
      try {
        const userId = userDoc.id;
        const userData = userDoc.data();
        
        console.log( Processing user: ${userId});

        // Get current balance data from admin panel
        const currentBalance = userData.currentBalance || 0;
        const totalEarnings = userData.totalEarnings || 0;
        const pendingWithdrawals = userData.pendingWithdrawals || 0;

        // Check if user has userBalances record (Flutter app format)
        const userBalanceRef = db.collection('userBalances').doc(userId);
        const userBalanceDoc = await userBalanceRef.get();

        if (userBalanceDoc.exists) {
          // Update existing userBalances record
          const existingBalance = userBalanceDoc.data();
          
          await userBalanceRef.update({
            availableBalance: currentBalance,
            totalEarnings: totalEarnings,
            pendingWithdrawals: pendingWithdrawals,
            incomingEarnings: existingBalance.incomingEarnings || 0, // Keep existing incoming
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            syncedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          
          console.log( Updated userBalances for ${userId}: available=$${currentBalance});
          syncedCount++;
        } else {
          // Create new userBalances record
          await userBalanceRef.set({
            userId: userId,
            availableBalance: currentBalance,
            incomingEarnings: 0, // Start with 0 incoming earnings
            totalEarnings: totalEarnings,
            pendingWithdrawals: pendingWithdrawals,
            totalWithdrawn: 0,
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            syncedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          
          console.log( Created userBalances for ${userId}: available=$${currentBalance});
          createdCount++;
        }

        // Also ensure users collection has all required fields
        await userDoc.ref.update({
          lastActivity: userData.lastActivity || admin.firestore.FieldValue.serverTimestamp(),
          syncedAt: admin.firestore.FieldValue.serverTimestamp(),
        });

      } catch (error) {
        console.error( Error processing user ${userDoc.id}:, error);
        errorCount++;
      }
    }

    console.log(' Synchronization Summary:');
    console.log(    Updated: ${syncedCount} users);
    console.log(    Created: ${createdCount} users);
    console.log(    Errors: ${errorCount} users);
    console.log(' User balance synchronization completed!');

  } catch (error) {
    console.error(' Error during synchronization:', error);
  }
}

async function syncOrderEarnings() {
  console.log(' Starting order earnings synchronization...');
  
  try {
    // Get all orders that are delivered but don't have earnings confirmed
    const ordersSnapshot = await db.collection('orders')
      .where('status', '==', 'Delivered')
      .get();
    
    console.log( Found ${ordersSnapshot.size} delivered orders);

    let processedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    for (const orderDoc of ordersSnapshot.docs) {
      try {
        const orderId = orderDoc.id;
        const orderData = orderDoc.data();
        const userId = orderData.userId;
        const totalAmount = orderData.totalAmount || 0;
        const earningsConfirmed = orderData.earningsConfirmed || false;

        if (!userId) {
          console.log( Skipping order ${orderId}: no userId);
          skippedCount++;
          continue;
        }

        if (earningsConfirmed) {
          console.log( Skipping order ${orderId}: earnings already confirmed);
          skippedCount++;
          continue;
        }

        // Calculate commission (10%)
        const commission = totalAmount * 0.10;

        if (commission <= 0) {
          console.log( Skipping order ${orderId}: no commission to process);
          skippedCount++;
          continue;
        }

        console.log( Processing earnings for order ${orderId}: $${commission.toFixed(2)});

        // Create or update earnings record
        const earningsQuery = await db.collection('earnings')
          .where('orderId', '==', orderId)
          .where('userId', '==', userId)
          .get();

        if (earningsQuery.empty) {
          // Create earnings record
          await db.collection('earnings').add({
            userId: userId,
            orderId: orderId,
            amount: commission,
            status: 'confirmed',
            orderStatus: 'delivered',
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            confirmedAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          console.log( Created earnings record for order ${orderId});
        }

        // Mark order earnings as confirmed
        await orderDoc.ref.update({
          earningsConfirmed: true,
          earningsConfirmedAt: admin.firestore.FieldValue.serverTimestamp(),
          updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        });

        processedCount++;

      } catch (error) {
        console.error( Error processing order ${orderDoc.id}:, error);
        errorCount++;
      }
    }

    console.log(' Order Earnings Sync Summary:');
    console.log(    Processed: ${processedCount} orders);
    console.log(    Skipped: ${skippedCount} orders);
    console.log(    Errors: ${errorCount} orders);
    console.log(' Order earnings synchronization completed!');

  } catch (error) {
    console.error(' Error during order earnings sync:', error);
  }
}

// Main execution
async function main() {
  console.log(' Starting complete balance synchronization...');
  console.log('=====================================');
  
  await syncUserBalances();
  console.log('');
  await syncOrderEarnings();
  
  console.log('');
  console.log(' Complete synchronization finished!');
  console.log('Your admin panel and Flutter app should now have consistent balance data.');
  
  process.exit(0);
}

// Run the migration
main().catch(console.error);{result.earningsAmount});
      
      // Also update order status
      await db.collection('orders').doc(orderId).update({
        status: 'Delivered',
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });
      
      return true;
    } else {
      console.error(' Earnings API call failed:', result.error);
      return false;
    }
  } catch (error) {
    console.error(' Error simulating admin delivery:', error);
    return false;
  }
}

async function checkBalances() {
  console.log(' Checking user balances...');
  
  try {
    // Check userBalances collection (Flutter app)
    const userBalanceDoc = await db.collection('userBalances').doc(TEST_USER_ID).get();
    const userBalance = userBalanceDoc.data();

    // Check users collection (admin panel)
    const userDoc = await db.collection('users').doc(TEST_USER_ID).get();
    const userData = userDoc.data();

    console.log(' Flutter App Balance (userBalances collection):');
    console.log(   Available Balance: // Migration Script: Sync User Balances Between Admin Panel and Flutter App
// This script ensures both collections have consistent data

const admin = require('firebase-admin');

// Initialize Firebase Admin (if not already initialized)
if (!admin.apps.length) {
  const serviceAccount = require('./dropshippingdinal-vq5iag-firebase-adminsdk-fbsvc-c5f412b27c.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    databaseURL: 'https://dropshippingdinal-vq5iag-default-rtdb.firebaseio.com'
  });
}

const db = admin.firestore();

async function syncUserBalances() {
  console.log(' Starting user balance synchronization...');
  
  try {
    // Get all users from the users collection (admin panel format)
    const usersSnapshot = await db.collection('users').get();
    console.log( Found ${usersSnapshot.size} users in admin panel collection);

    let syncedCount = 0;
    let createdCount = 0;
    let errorCount = 0;

    for (const userDoc of usersSnapshot.docs) {
      try {
        const userId = userDoc.id;
        const userData = userDoc.data();
        
        console.log( Processing user: ${userId});

        // Get current balance data from admin panel
        const currentBalance = userData.currentBalance || 0;
        const totalEarnings = userData.totalEarnings || 0;
        const pendingWithdrawals = userData.pendingWithdrawals || 0;

        // Check if user has userBalances record (Flutter app format)
        const userBalanceRef = db.collection('userBalances').doc(userId);
        const userBalanceDoc = await userBalanceRef.get();

        if (userBalanceDoc.exists) {
          // Update existing userBalances record
          const existingBalance = userBalanceDoc.data();
          
          await userBalanceRef.update({
            availableBalance: currentBalance,
            totalEarnings: totalEarnings,
            pendingWithdrawals: pendingWithdrawals,
            incomingEarnings: existingBalance.incomingEarnings || 0, // Keep existing incoming
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            syncedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          
          console.log( Updated userBalances for ${userId}: available=$${currentBalance});
          syncedCount++;
        } else {
          // Create new userBalances record
          await userBalanceRef.set({
            userId: userId,
            availableBalance: currentBalance,
            incomingEarnings: 0, // Start with 0 incoming earnings
            totalEarnings: totalEarnings,
            pendingWithdrawals: pendingWithdrawals,
            totalWithdrawn: 0,
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            syncedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          
          console.log( Created userBalances for ${userId}: available=$${currentBalance});
          createdCount++;
        }

        // Also ensure users collection has all required fields
        await userDoc.ref.update({
          lastActivity: userData.lastActivity || admin.firestore.FieldValue.serverTimestamp(),
          syncedAt: admin.firestore.FieldValue.serverTimestamp(),
        });

      } catch (error) {
        console.error( Error processing user ${userDoc.id}:, error);
        errorCount++;
      }
    }

    console.log(' Synchronization Summary:');
    console.log(    Updated: ${syncedCount} users);
    console.log(    Created: ${createdCount} users);
    console.log(    Errors: ${errorCount} users);
    console.log(' User balance synchronization completed!');

  } catch (error) {
    console.error(' Error during synchronization:', error);
  }
}

async function syncOrderEarnings() {
  console.log(' Starting order earnings synchronization...');
  
  try {
    // Get all orders that are delivered but don't have earnings confirmed
    const ordersSnapshot = await db.collection('orders')
      .where('status', '==', 'Delivered')
      .get();
    
    console.log( Found ${ordersSnapshot.size} delivered orders);

    let processedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    for (const orderDoc of ordersSnapshot.docs) {
      try {
        const orderId = orderDoc.id;
        const orderData = orderDoc.data();
        const userId = orderData.userId;
        const totalAmount = orderData.totalAmount || 0;
        const earningsConfirmed = orderData.earningsConfirmed || false;

        if (!userId) {
          console.log( Skipping order ${orderId}: no userId);
          skippedCount++;
          continue;
        }

        if (earningsConfirmed) {
          console.log( Skipping order ${orderId}: earnings already confirmed);
          skippedCount++;
          continue;
        }

        // Calculate commission (10%)
        const commission = totalAmount * 0.10;

        if (commission <= 0) {
          console.log( Skipping order ${orderId}: no commission to process);
          skippedCount++;
          continue;
        }

        console.log( Processing earnings for order ${orderId}: $${commission.toFixed(2)});

        // Create or update earnings record
        const earningsQuery = await db.collection('earnings')
          .where('orderId', '==', orderId)
          .where('userId', '==', userId)
          .get();

        if (earningsQuery.empty) {
          // Create earnings record
          await db.collection('earnings').add({
            userId: userId,
            orderId: orderId,
            amount: commission,
            status: 'confirmed',
            orderStatus: 'delivered',
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            confirmedAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          console.log( Created earnings record for order ${orderId});
        }

        // Mark order earnings as confirmed
        await orderDoc.ref.update({
          earningsConfirmed: true,
          earningsConfirmedAt: admin.firestore.FieldValue.serverTimestamp(),
          updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        });

        processedCount++;

      } catch (error) {
        console.error( Error processing order ${orderDoc.id}:, error);
        errorCount++;
      }
    }

    console.log(' Order Earnings Sync Summary:');
    console.log(    Processed: ${processedCount} orders);
    console.log(    Skipped: ${skippedCount} orders);
    console.log(    Errors: ${errorCount} orders);
    console.log(' Order earnings synchronization completed!');

  } catch (error) {
    console.error(' Error during order earnings sync:', error);
  }
}

// Main execution
async function main() {
  console.log(' Starting complete balance synchronization...');
  console.log('=====================================');
  
  await syncUserBalances();
  console.log('');
  await syncOrderEarnings();
  
  console.log('');
  console.log(' Complete synchronization finished!');
  console.log('Your admin panel and Flutter app should now have consistent balance data.');
  
  process.exit(0);
}

// Run the migration
main().catch(console.error);{userBalance?.availableBalance || 0});
    console.log(   Incoming Earnings: // Migration Script: Sync User Balances Between Admin Panel and Flutter App
// This script ensures both collections have consistent data

const admin = require('firebase-admin');

// Initialize Firebase Admin (if not already initialized)
if (!admin.apps.length) {
  const serviceAccount = require('./dropshippingdinal-vq5iag-firebase-adminsdk-fbsvc-c5f412b27c.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    databaseURL: 'https://dropshippingdinal-vq5iag-default-rtdb.firebaseio.com'
  });
}

const db = admin.firestore();

async function syncUserBalances() {
  console.log(' Starting user balance synchronization...');
  
  try {
    // Get all users from the users collection (admin panel format)
    const usersSnapshot = await db.collection('users').get();
    console.log( Found ${usersSnapshot.size} users in admin panel collection);

    let syncedCount = 0;
    let createdCount = 0;
    let errorCount = 0;

    for (const userDoc of usersSnapshot.docs) {
      try {
        const userId = userDoc.id;
        const userData = userDoc.data();
        
        console.log( Processing user: ${userId});

        // Get current balance data from admin panel
        const currentBalance = userData.currentBalance || 0;
        const totalEarnings = userData.totalEarnings || 0;
        const pendingWithdrawals = userData.pendingWithdrawals || 0;

        // Check if user has userBalances record (Flutter app format)
        const userBalanceRef = db.collection('userBalances').doc(userId);
        const userBalanceDoc = await userBalanceRef.get();

        if (userBalanceDoc.exists) {
          // Update existing userBalances record
          const existingBalance = userBalanceDoc.data();
          
          await userBalanceRef.update({
            availableBalance: currentBalance,
            totalEarnings: totalEarnings,
            pendingWithdrawals: pendingWithdrawals,
            incomingEarnings: existingBalance.incomingEarnings || 0, // Keep existing incoming
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            syncedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          
          console.log( Updated userBalances for ${userId}: available=$${currentBalance});
          syncedCount++;
        } else {
          // Create new userBalances record
          await userBalanceRef.set({
            userId: userId,
            availableBalance: currentBalance,
            incomingEarnings: 0, // Start with 0 incoming earnings
            totalEarnings: totalEarnings,
            pendingWithdrawals: pendingWithdrawals,
            totalWithdrawn: 0,
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            syncedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          
          console.log( Created userBalances for ${userId}: available=$${currentBalance});
          createdCount++;
        }

        // Also ensure users collection has all required fields
        await userDoc.ref.update({
          lastActivity: userData.lastActivity || admin.firestore.FieldValue.serverTimestamp(),
          syncedAt: admin.firestore.FieldValue.serverTimestamp(),
        });

      } catch (error) {
        console.error( Error processing user ${userDoc.id}:, error);
        errorCount++;
      }
    }

    console.log(' Synchronization Summary:');
    console.log(    Updated: ${syncedCount} users);
    console.log(    Created: ${createdCount} users);
    console.log(    Errors: ${errorCount} users);
    console.log(' User balance synchronization completed!');

  } catch (error) {
    console.error(' Error during synchronization:', error);
  }
}

async function syncOrderEarnings() {
  console.log(' Starting order earnings synchronization...');
  
  try {
    // Get all orders that are delivered but don't have earnings confirmed
    const ordersSnapshot = await db.collection('orders')
      .where('status', '==', 'Delivered')
      .get();
    
    console.log( Found ${ordersSnapshot.size} delivered orders);

    let processedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    for (const orderDoc of ordersSnapshot.docs) {
      try {
        const orderId = orderDoc.id;
        const orderData = orderDoc.data();
        const userId = orderData.userId;
        const totalAmount = orderData.totalAmount || 0;
        const earningsConfirmed = orderData.earningsConfirmed || false;

        if (!userId) {
          console.log( Skipping order ${orderId}: no userId);
          skippedCount++;
          continue;
        }

        if (earningsConfirmed) {
          console.log( Skipping order ${orderId}: earnings already confirmed);
          skippedCount++;
          continue;
        }

        // Calculate commission (10%)
        const commission = totalAmount * 0.10;

        if (commission <= 0) {
          console.log( Skipping order ${orderId}: no commission to process);
          skippedCount++;
          continue;
        }

        console.log( Processing earnings for order ${orderId}: $${commission.toFixed(2)});

        // Create or update earnings record
        const earningsQuery = await db.collection('earnings')
          .where('orderId', '==', orderId)
          .where('userId', '==', userId)
          .get();

        if (earningsQuery.empty) {
          // Create earnings record
          await db.collection('earnings').add({
            userId: userId,
            orderId: orderId,
            amount: commission,
            status: 'confirmed',
            orderStatus: 'delivered',
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            confirmedAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          console.log( Created earnings record for order ${orderId});
        }

        // Mark order earnings as confirmed
        await orderDoc.ref.update({
          earningsConfirmed: true,
          earningsConfirmedAt: admin.firestore.FieldValue.serverTimestamp(),
          updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        });

        processedCount++;

      } catch (error) {
        console.error( Error processing order ${orderDoc.id}:, error);
        errorCount++;
      }
    }

    console.log(' Order Earnings Sync Summary:');
    console.log(    Processed: ${processedCount} orders);
    console.log(    Skipped: ${skippedCount} orders);
    console.log(    Errors: ${errorCount} orders);
    console.log(' Order earnings synchronization completed!');

  } catch (error) {
    console.error(' Error during order earnings sync:', error);
  }
}

// Main execution
async function main() {
  console.log(' Starting complete balance synchronization...');
  console.log('=====================================');
  
  await syncUserBalances();
  console.log('');
  await syncOrderEarnings();
  
  console.log('');
  console.log(' Complete synchronization finished!');
  console.log('Your admin panel and Flutter app should now have consistent balance data.');
  
  process.exit(0);
}

// Run the migration
main().catch(console.error);{userBalance?.incomingEarnings || 0});
    console.log(   Total Earnings: // Migration Script: Sync User Balances Between Admin Panel and Flutter App
// This script ensures both collections have consistent data

const admin = require('firebase-admin');

// Initialize Firebase Admin (if not already initialized)
if (!admin.apps.length) {
  const serviceAccount = require('./dropshippingdinal-vq5iag-firebase-adminsdk-fbsvc-c5f412b27c.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    databaseURL: 'https://dropshippingdinal-vq5iag-default-rtdb.firebaseio.com'
  });
}

const db = admin.firestore();

async function syncUserBalances() {
  console.log(' Starting user balance synchronization...');
  
  try {
    // Get all users from the users collection (admin panel format)
    const usersSnapshot = await db.collection('users').get();
    console.log( Found ${usersSnapshot.size} users in admin panel collection);

    let syncedCount = 0;
    let createdCount = 0;
    let errorCount = 0;

    for (const userDoc of usersSnapshot.docs) {
      try {
        const userId = userDoc.id;
        const userData = userDoc.data();
        
        console.log( Processing user: ${userId});

        // Get current balance data from admin panel
        const currentBalance = userData.currentBalance || 0;
        const totalEarnings = userData.totalEarnings || 0;
        const pendingWithdrawals = userData.pendingWithdrawals || 0;

        // Check if user has userBalances record (Flutter app format)
        const userBalanceRef = db.collection('userBalances').doc(userId);
        const userBalanceDoc = await userBalanceRef.get();

        if (userBalanceDoc.exists) {
          // Update existing userBalances record
          const existingBalance = userBalanceDoc.data();
          
          await userBalanceRef.update({
            availableBalance: currentBalance,
            totalEarnings: totalEarnings,
            pendingWithdrawals: pendingWithdrawals,
            incomingEarnings: existingBalance.incomingEarnings || 0, // Keep existing incoming
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            syncedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          
          console.log( Updated userBalances for ${userId}: available=$${currentBalance});
          syncedCount++;
        } else {
          // Create new userBalances record
          await userBalanceRef.set({
            userId: userId,
            availableBalance: currentBalance,
            incomingEarnings: 0, // Start with 0 incoming earnings
            totalEarnings: totalEarnings,
            pendingWithdrawals: pendingWithdrawals,
            totalWithdrawn: 0,
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            syncedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          
          console.log( Created userBalances for ${userId}: available=$${currentBalance});
          createdCount++;
        }

        // Also ensure users collection has all required fields
        await userDoc.ref.update({
          lastActivity: userData.lastActivity || admin.firestore.FieldValue.serverTimestamp(),
          syncedAt: admin.firestore.FieldValue.serverTimestamp(),
        });

      } catch (error) {
        console.error( Error processing user ${userDoc.id}:, error);
        errorCount++;
      }
    }

    console.log(' Synchronization Summary:');
    console.log(    Updated: ${syncedCount} users);
    console.log(    Created: ${createdCount} users);
    console.log(    Errors: ${errorCount} users);
    console.log(' User balance synchronization completed!');

  } catch (error) {
    console.error(' Error during synchronization:', error);
  }
}

async function syncOrderEarnings() {
  console.log(' Starting order earnings synchronization...');
  
  try {
    // Get all orders that are delivered but don't have earnings confirmed
    const ordersSnapshot = await db.collection('orders')
      .where('status', '==', 'Delivered')
      .get();
    
    console.log( Found ${ordersSnapshot.size} delivered orders);

    let processedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    for (const orderDoc of ordersSnapshot.docs) {
      try {
        const orderId = orderDoc.id;
        const orderData = orderDoc.data();
        const userId = orderData.userId;
        const totalAmount = orderData.totalAmount || 0;
        const earningsConfirmed = orderData.earningsConfirmed || false;

        if (!userId) {
          console.log( Skipping order ${orderId}: no userId);
          skippedCount++;
          continue;
        }

        if (earningsConfirmed) {
          console.log( Skipping order ${orderId}: earnings already confirmed);
          skippedCount++;
          continue;
        }

        // Calculate commission (10%)
        const commission = totalAmount * 0.10;

        if (commission <= 0) {
          console.log( Skipping order ${orderId}: no commission to process);
          skippedCount++;
          continue;
        }

        console.log( Processing earnings for order ${orderId}: $${commission.toFixed(2)});

        // Create or update earnings record
        const earningsQuery = await db.collection('earnings')
          .where('orderId', '==', orderId)
          .where('userId', '==', userId)
          .get();

        if (earningsQuery.empty) {
          // Create earnings record
          await db.collection('earnings').add({
            userId: userId,
            orderId: orderId,
            amount: commission,
            status: 'confirmed',
            orderStatus: 'delivered',
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            confirmedAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          console.log( Created earnings record for order ${orderId});
        }

        // Mark order earnings as confirmed
        await orderDoc.ref.update({
          earningsConfirmed: true,
          earningsConfirmedAt: admin.firestore.FieldValue.serverTimestamp(),
          updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        });

        processedCount++;

      } catch (error) {
        console.error( Error processing order ${orderDoc.id}:, error);
        errorCount++;
      }
    }

    console.log(' Order Earnings Sync Summary:');
    console.log(    Processed: ${processedCount} orders);
    console.log(    Skipped: ${skippedCount} orders);
    console.log(    Errors: ${errorCount} orders);
    console.log(' Order earnings synchronization completed!');

  } catch (error) {
    console.error(' Error during order earnings sync:', error);
  }
}

// Main execution
async function main() {
  console.log(' Starting complete balance synchronization...');
  console.log('=====================================');
  
  await syncUserBalances();
  console.log('');
  await syncOrderEarnings();
  
  console.log('');
  console.log(' Complete synchronization finished!');
  console.log('Your admin panel and Flutter app should now have consistent balance data.');
  
  process.exit(0);
}

// Run the migration
main().catch(console.error);{userBalance?.totalEarnings || 0});

    console.log(' Admin Panel Balance (users collection):');
    console.log(   Current Balance: // Migration Script: Sync User Balances Between Admin Panel and Flutter App
// This script ensures both collections have consistent data

const admin = require('firebase-admin');

// Initialize Firebase Admin (if not already initialized)
if (!admin.apps.length) {
  const serviceAccount = require('./dropshippingdinal-vq5iag-firebase-adminsdk-fbsvc-c5f412b27c.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    databaseURL: 'https://dropshippingdinal-vq5iag-default-rtdb.firebaseio.com'
  });
}

const db = admin.firestore();

async function syncUserBalances() {
  console.log(' Starting user balance synchronization...');
  
  try {
    // Get all users from the users collection (admin panel format)
    const usersSnapshot = await db.collection('users').get();
    console.log( Found ${usersSnapshot.size} users in admin panel collection);

    let syncedCount = 0;
    let createdCount = 0;
    let errorCount = 0;

    for (const userDoc of usersSnapshot.docs) {
      try {
        const userId = userDoc.id;
        const userData = userDoc.data();
        
        console.log( Processing user: ${userId});

        // Get current balance data from admin panel
        const currentBalance = userData.currentBalance || 0;
        const totalEarnings = userData.totalEarnings || 0;
        const pendingWithdrawals = userData.pendingWithdrawals || 0;

        // Check if user has userBalances record (Flutter app format)
        const userBalanceRef = db.collection('userBalances').doc(userId);
        const userBalanceDoc = await userBalanceRef.get();

        if (userBalanceDoc.exists) {
          // Update existing userBalances record
          const existingBalance = userBalanceDoc.data();
          
          await userBalanceRef.update({
            availableBalance: currentBalance,
            totalEarnings: totalEarnings,
            pendingWithdrawals: pendingWithdrawals,
            incomingEarnings: existingBalance.incomingEarnings || 0, // Keep existing incoming
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            syncedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          
          console.log( Updated userBalances for ${userId}: available=$${currentBalance});
          syncedCount++;
        } else {
          // Create new userBalances record
          await userBalanceRef.set({
            userId: userId,
            availableBalance: currentBalance,
            incomingEarnings: 0, // Start with 0 incoming earnings
            totalEarnings: totalEarnings,
            pendingWithdrawals: pendingWithdrawals,
            totalWithdrawn: 0,
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            syncedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          
          console.log( Created userBalances for ${userId}: available=$${currentBalance});
          createdCount++;
        }

        // Also ensure users collection has all required fields
        await userDoc.ref.update({
          lastActivity: userData.lastActivity || admin.firestore.FieldValue.serverTimestamp(),
          syncedAt: admin.firestore.FieldValue.serverTimestamp(),
        });

      } catch (error) {
        console.error( Error processing user ${userDoc.id}:, error);
        errorCount++;
      }
    }

    console.log(' Synchronization Summary:');
    console.log(    Updated: ${syncedCount} users);
    console.log(    Created: ${createdCount} users);
    console.log(    Errors: ${errorCount} users);
    console.log(' User balance synchronization completed!');

  } catch (error) {
    console.error(' Error during synchronization:', error);
  }
}

async function syncOrderEarnings() {
  console.log(' Starting order earnings synchronization...');
  
  try {
    // Get all orders that are delivered but don't have earnings confirmed
    const ordersSnapshot = await db.collection('orders')
      .where('status', '==', 'Delivered')
      .get();
    
    console.log( Found ${ordersSnapshot.size} delivered orders);

    let processedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    for (const orderDoc of ordersSnapshot.docs) {
      try {
        const orderId = orderDoc.id;
        const orderData = orderDoc.data();
        const userId = orderData.userId;
        const totalAmount = orderData.totalAmount || 0;
        const earningsConfirmed = orderData.earningsConfirmed || false;

        if (!userId) {
          console.log( Skipping order ${orderId}: no userId);
          skippedCount++;
          continue;
        }

        if (earningsConfirmed) {
          console.log( Skipping order ${orderId}: earnings already confirmed);
          skippedCount++;
          continue;
        }

        // Calculate commission (10%)
        const commission = totalAmount * 0.10;

        if (commission <= 0) {
          console.log( Skipping order ${orderId}: no commission to process);
          skippedCount++;
          continue;
        }

        console.log( Processing earnings for order ${orderId}: $${commission.toFixed(2)});

        // Create or update earnings record
        const earningsQuery = await db.collection('earnings')
          .where('orderId', '==', orderId)
          .where('userId', '==', userId)
          .get();

        if (earningsQuery.empty) {
          // Create earnings record
          await db.collection('earnings').add({
            userId: userId,
            orderId: orderId,
            amount: commission,
            status: 'confirmed',
            orderStatus: 'delivered',
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            confirmedAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          console.log( Created earnings record for order ${orderId});
        }

        // Mark order earnings as confirmed
        await orderDoc.ref.update({
          earningsConfirmed: true,
          earningsConfirmedAt: admin.firestore.FieldValue.serverTimestamp(),
          updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        });

        processedCount++;

      } catch (error) {
        console.error( Error processing order ${orderDoc.id}:, error);
        errorCount++;
      }
    }

    console.log(' Order Earnings Sync Summary:');
    console.log(    Processed: ${processedCount} orders);
    console.log(    Skipped: ${skippedCount} orders);
    console.log(    Errors: ${errorCount} orders);
    console.log(' Order earnings synchronization completed!');

  } catch (error) {
    console.error(' Error during order earnings sync:', error);
  }
}

// Main execution
async function main() {
  console.log(' Starting complete balance synchronization...');
  console.log('=====================================');
  
  await syncUserBalances();
  console.log('');
  await syncOrderEarnings();
  
  console.log('');
  console.log(' Complete synchronization finished!');
  console.log('Your admin panel and Flutter app should now have consistent balance data.');
  
  process.exit(0);
}

// Run the migration
main().catch(console.error);{userData?.currentBalance || 0});
    console.log(   Total Earnings: // Migration Script: Sync User Balances Between Admin Panel and Flutter App
// This script ensures both collections have consistent data

const admin = require('firebase-admin');

// Initialize Firebase Admin (if not already initialized)
if (!admin.apps.length) {
  const serviceAccount = require('./dropshippingdinal-vq5iag-firebase-adminsdk-fbsvc-c5f412b27c.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    databaseURL: 'https://dropshippingdinal-vq5iag-default-rtdb.firebaseio.com'
  });
}

const db = admin.firestore();

async function syncUserBalances() {
  console.log(' Starting user balance synchronization...');
  
  try {
    // Get all users from the users collection (admin panel format)
    const usersSnapshot = await db.collection('users').get();
    console.log( Found ${usersSnapshot.size} users in admin panel collection);

    let syncedCount = 0;
    let createdCount = 0;
    let errorCount = 0;

    for (const userDoc of usersSnapshot.docs) {
      try {
        const userId = userDoc.id;
        const userData = userDoc.data();
        
        console.log( Processing user: ${userId});

        // Get current balance data from admin panel
        const currentBalance = userData.currentBalance || 0;
        const totalEarnings = userData.totalEarnings || 0;
        const pendingWithdrawals = userData.pendingWithdrawals || 0;

        // Check if user has userBalances record (Flutter app format)
        const userBalanceRef = db.collection('userBalances').doc(userId);
        const userBalanceDoc = await userBalanceRef.get();

        if (userBalanceDoc.exists) {
          // Update existing userBalances record
          const existingBalance = userBalanceDoc.data();
          
          await userBalanceRef.update({
            availableBalance: currentBalance,
            totalEarnings: totalEarnings,
            pendingWithdrawals: pendingWithdrawals,
            incomingEarnings: existingBalance.incomingEarnings || 0, // Keep existing incoming
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            syncedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          
          console.log( Updated userBalances for ${userId}: available=$${currentBalance});
          syncedCount++;
        } else {
          // Create new userBalances record
          await userBalanceRef.set({
            userId: userId,
            availableBalance: currentBalance,
            incomingEarnings: 0, // Start with 0 incoming earnings
            totalEarnings: totalEarnings,
            pendingWithdrawals: pendingWithdrawals,
            totalWithdrawn: 0,
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            syncedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          
          console.log( Created userBalances for ${userId}: available=$${currentBalance});
          createdCount++;
        }

        // Also ensure users collection has all required fields
        await userDoc.ref.update({
          lastActivity: userData.lastActivity || admin.firestore.FieldValue.serverTimestamp(),
          syncedAt: admin.firestore.FieldValue.serverTimestamp(),
        });

      } catch (error) {
        console.error( Error processing user ${userDoc.id}:, error);
        errorCount++;
      }
    }

    console.log(' Synchronization Summary:');
    console.log(    Updated: ${syncedCount} users);
    console.log(    Created: ${createdCount} users);
    console.log(    Errors: ${errorCount} users);
    console.log(' User balance synchronization completed!');

  } catch (error) {
    console.error(' Error during synchronization:', error);
  }
}

async function syncOrderEarnings() {
  console.log(' Starting order earnings synchronization...');
  
  try {
    // Get all orders that are delivered but don't have earnings confirmed
    const ordersSnapshot = await db.collection('orders')
      .where('status', '==', 'Delivered')
      .get();
    
    console.log( Found ${ordersSnapshot.size} delivered orders);

    let processedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    for (const orderDoc of ordersSnapshot.docs) {
      try {
        const orderId = orderDoc.id;
        const orderData = orderDoc.data();
        const userId = orderData.userId;
        const totalAmount = orderData.totalAmount || 0;
        const earningsConfirmed = orderData.earningsConfirmed || false;

        if (!userId) {
          console.log( Skipping order ${orderId}: no userId);
          skippedCount++;
          continue;
        }

        if (earningsConfirmed) {
          console.log( Skipping order ${orderId}: earnings already confirmed);
          skippedCount++;
          continue;
        }

        // Calculate commission (10%)
        const commission = totalAmount * 0.10;

        if (commission <= 0) {
          console.log( Skipping order ${orderId}: no commission to process);
          skippedCount++;
          continue;
        }

        console.log( Processing earnings for order ${orderId}: $${commission.toFixed(2)});

        // Create or update earnings record
        const earningsQuery = await db.collection('earnings')
          .where('orderId', '==', orderId)
          .where('userId', '==', userId)
          .get();

        if (earningsQuery.empty) {
          // Create earnings record
          await db.collection('earnings').add({
            userId: userId,
            orderId: orderId,
            amount: commission,
            status: 'confirmed',
            orderStatus: 'delivered',
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            confirmedAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          console.log( Created earnings record for order ${orderId});
        }

        // Mark order earnings as confirmed
        await orderDoc.ref.update({
          earningsConfirmed: true,
          earningsConfirmedAt: admin.firestore.FieldValue.serverTimestamp(),
          updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        });

        processedCount++;

      } catch (error) {
        console.error( Error processing order ${orderDoc.id}:, error);
        errorCount++;
      }
    }

    console.log(' Order Earnings Sync Summary:');
    console.log(    Processed: ${processedCount} orders);
    console.log(    Skipped: ${skippedCount} orders);
    console.log(    Errors: ${errorCount} orders);
    console.log(' Order earnings synchronization completed!');

  } catch (error) {
    console.error(' Error during order earnings sync:', error);
  }
}

// Main execution
async function main() {
  console.log(' Starting complete balance synchronization...');
  console.log('=====================================');
  
  await syncUserBalances();
  console.log('');
  await syncOrderEarnings();
  
  console.log('');
  console.log(' Complete synchronization finished!');
  console.log('Your admin panel and Flutter app should now have consistent balance data.');
  
  process.exit(0);
}

// Run the migration
main().catch(console.error);{userData?.totalEarnings || 0});

    // Check if earnings moved correctly
    const expectedAvailable = 10.0;
    const expectedIncoming = 0.0;
    
    if (userBalance?.availableBalance === expectedAvailable && 
        userBalance?.incomingEarnings === expectedIncoming) {
      console.log(' EARNINGS FLOW TEST PASSED!');
      console.log('   Earnings successfully moved from incoming to available balance');
      return true;
    } else {
      console.log(' EARNINGS FLOW TEST FAILED!');
      console.log(   Expected: Available=// Migration Script: Sync User Balances Between Admin Panel and Flutter App
// This script ensures both collections have consistent data

const admin = require('firebase-admin');

// Initialize Firebase Admin (if not already initialized)
if (!admin.apps.length) {
  const serviceAccount = require('./dropshippingdinal-vq5iag-firebase-adminsdk-fbsvc-c5f412b27c.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    databaseURL: 'https://dropshippingdinal-vq5iag-default-rtdb.firebaseio.com'
  });
}

const db = admin.firestore();

async function syncUserBalances() {
  console.log(' Starting user balance synchronization...');
  
  try {
    // Get all users from the users collection (admin panel format)
    const usersSnapshot = await db.collection('users').get();
    console.log( Found ${usersSnapshot.size} users in admin panel collection);

    let syncedCount = 0;
    let createdCount = 0;
    let errorCount = 0;

    for (const userDoc of usersSnapshot.docs) {
      try {
        const userId = userDoc.id;
        const userData = userDoc.data();
        
        console.log( Processing user: ${userId});

        // Get current balance data from admin panel
        const currentBalance = userData.currentBalance || 0;
        const totalEarnings = userData.totalEarnings || 0;
        const pendingWithdrawals = userData.pendingWithdrawals || 0;

        // Check if user has userBalances record (Flutter app format)
        const userBalanceRef = db.collection('userBalances').doc(userId);
        const userBalanceDoc = await userBalanceRef.get();

        if (userBalanceDoc.exists) {
          // Update existing userBalances record
          const existingBalance = userBalanceDoc.data();
          
          await userBalanceRef.update({
            availableBalance: currentBalance,
            totalEarnings: totalEarnings,
            pendingWithdrawals: pendingWithdrawals,
            incomingEarnings: existingBalance.incomingEarnings || 0, // Keep existing incoming
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            syncedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          
          console.log( Updated userBalances for ${userId}: available=$${currentBalance});
          syncedCount++;
        } else {
          // Create new userBalances record
          await userBalanceRef.set({
            userId: userId,
            availableBalance: currentBalance,
            incomingEarnings: 0, // Start with 0 incoming earnings
            totalEarnings: totalEarnings,
            pendingWithdrawals: pendingWithdrawals,
            totalWithdrawn: 0,
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            syncedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          
          console.log( Created userBalances for ${userId}: available=$${currentBalance});
          createdCount++;
        }

        // Also ensure users collection has all required fields
        await userDoc.ref.update({
          lastActivity: userData.lastActivity || admin.firestore.FieldValue.serverTimestamp(),
          syncedAt: admin.firestore.FieldValue.serverTimestamp(),
        });

      } catch (error) {
        console.error( Error processing user ${userDoc.id}:, error);
        errorCount++;
      }
    }

    console.log(' Synchronization Summary:');
    console.log(    Updated: ${syncedCount} users);
    console.log(    Created: ${createdCount} users);
    console.log(    Errors: ${errorCount} users);
    console.log(' User balance synchronization completed!');

  } catch (error) {
    console.error(' Error during synchronization:', error);
  }
}

async function syncOrderEarnings() {
  console.log(' Starting order earnings synchronization...');
  
  try {
    // Get all orders that are delivered but don't have earnings confirmed
    const ordersSnapshot = await db.collection('orders')
      .where('status', '==', 'Delivered')
      .get();
    
    console.log( Found ${ordersSnapshot.size} delivered orders);

    let processedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    for (const orderDoc of ordersSnapshot.docs) {
      try {
        const orderId = orderDoc.id;
        const orderData = orderDoc.data();
        const userId = orderData.userId;
        const totalAmount = orderData.totalAmount || 0;
        const earningsConfirmed = orderData.earningsConfirmed || false;

        if (!userId) {
          console.log( Skipping order ${orderId}: no userId);
          skippedCount++;
          continue;
        }

        if (earningsConfirmed) {
          console.log( Skipping order ${orderId}: earnings already confirmed);
          skippedCount++;
          continue;
        }

        // Calculate commission (10%)
        const commission = totalAmount * 0.10;

        if (commission <= 0) {
          console.log( Skipping order ${orderId}: no commission to process);
          skippedCount++;
          continue;
        }

        console.log( Processing earnings for order ${orderId}: $${commission.toFixed(2)});

        // Create or update earnings record
        const earningsQuery = await db.collection('earnings')
          .where('orderId', '==', orderId)
          .where('userId', '==', userId)
          .get();

        if (earningsQuery.empty) {
          // Create earnings record
          await db.collection('earnings').add({
            userId: userId,
            orderId: orderId,
            amount: commission,
            status: 'confirmed',
            orderStatus: 'delivered',
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            confirmedAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          console.log( Created earnings record for order ${orderId});
        }

        // Mark order earnings as confirmed
        await orderDoc.ref.update({
          earningsConfirmed: true,
          earningsConfirmedAt: admin.firestore.FieldValue.serverTimestamp(),
          updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        });

        processedCount++;

      } catch (error) {
        console.error( Error processing order ${orderDoc.id}:, error);
        errorCount++;
      }
    }

    console.log(' Order Earnings Sync Summary:');
    console.log(    Processed: ${processedCount} orders);
    console.log(    Skipped: ${skippedCount} orders);
    console.log(    Errors: ${errorCount} orders);
    console.log(' Order earnings synchronization completed!');

  } catch (error) {
    console.error(' Error during order earnings sync:', error);
  }
}

// Main execution
async function main() {
  console.log(' Starting complete balance synchronization...');
  console.log('=====================================');
  
  await syncUserBalances();
  console.log('');
  await syncOrderEarnings();
  
  console.log('');
  console.log(' Complete synchronization finished!');
  console.log('Your admin panel and Flutter app should now have consistent balance data.');
  
  process.exit(0);
}

// Run the migration
main().catch(console.error);{expectedAvailable}, Incoming=// Migration Script: Sync User Balances Between Admin Panel and Flutter App
// This script ensures both collections have consistent data

const admin = require('firebase-admin');

// Initialize Firebase Admin (if not already initialized)
if (!admin.apps.length) {
  const serviceAccount = require('./dropshippingdinal-vq5iag-firebase-adminsdk-fbsvc-c5f412b27c.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    databaseURL: 'https://dropshippingdinal-vq5iag-default-rtdb.firebaseio.com'
  });
}

const db = admin.firestore();

async function syncUserBalances() {
  console.log(' Starting user balance synchronization...');
  
  try {
    // Get all users from the users collection (admin panel format)
    const usersSnapshot = await db.collection('users').get();
    console.log( Found ${usersSnapshot.size} users in admin panel collection);

    let syncedCount = 0;
    let createdCount = 0;
    let errorCount = 0;

    for (const userDoc of usersSnapshot.docs) {
      try {
        const userId = userDoc.id;
        const userData = userDoc.data();
        
        console.log( Processing user: ${userId});

        // Get current balance data from admin panel
        const currentBalance = userData.currentBalance || 0;
        const totalEarnings = userData.totalEarnings || 0;
        const pendingWithdrawals = userData.pendingWithdrawals || 0;

        // Check if user has userBalances record (Flutter app format)
        const userBalanceRef = db.collection('userBalances').doc(userId);
        const userBalanceDoc = await userBalanceRef.get();

        if (userBalanceDoc.exists) {
          // Update existing userBalances record
          const existingBalance = userBalanceDoc.data();
          
          await userBalanceRef.update({
            availableBalance: currentBalance,
            totalEarnings: totalEarnings,
            pendingWithdrawals: pendingWithdrawals,
            incomingEarnings: existingBalance.incomingEarnings || 0, // Keep existing incoming
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            syncedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          
          console.log( Updated userBalances for ${userId}: available=$${currentBalance});
          syncedCount++;
        } else {
          // Create new userBalances record
          await userBalanceRef.set({
            userId: userId,
            availableBalance: currentBalance,
            incomingEarnings: 0, // Start with 0 incoming earnings
            totalEarnings: totalEarnings,
            pendingWithdrawals: pendingWithdrawals,
            totalWithdrawn: 0,
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            syncedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          
          console.log( Created userBalances for ${userId}: available=$${currentBalance});
          createdCount++;
        }

        // Also ensure users collection has all required fields
        await userDoc.ref.update({
          lastActivity: userData.lastActivity || admin.firestore.FieldValue.serverTimestamp(),
          syncedAt: admin.firestore.FieldValue.serverTimestamp(),
        });

      } catch (error) {
        console.error( Error processing user ${userDoc.id}:, error);
        errorCount++;
      }
    }

    console.log(' Synchronization Summary:');
    console.log(    Updated: ${syncedCount} users);
    console.log(    Created: ${createdCount} users);
    console.log(    Errors: ${errorCount} users);
    console.log(' User balance synchronization completed!');

  } catch (error) {
    console.error(' Error during synchronization:', error);
  }
}

async function syncOrderEarnings() {
  console.log(' Starting order earnings synchronization...');
  
  try {
    // Get all orders that are delivered but don't have earnings confirmed
    const ordersSnapshot = await db.collection('orders')
      .where('status', '==', 'Delivered')
      .get();
    
    console.log( Found ${ordersSnapshot.size} delivered orders);

    let processedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    for (const orderDoc of ordersSnapshot.docs) {
      try {
        const orderId = orderDoc.id;
        const orderData = orderDoc.data();
        const userId = orderData.userId;
        const totalAmount = orderData.totalAmount || 0;
        const earningsConfirmed = orderData.earningsConfirmed || false;

        if (!userId) {
          console.log( Skipping order ${orderId}: no userId);
          skippedCount++;
          continue;
        }

        if (earningsConfirmed) {
          console.log( Skipping order ${orderId}: earnings already confirmed);
          skippedCount++;
          continue;
        }

        // Calculate commission (10%)
        const commission = totalAmount * 0.10;

        if (commission <= 0) {
          console.log( Skipping order ${orderId}: no commission to process);
          skippedCount++;
          continue;
        }

        console.log( Processing earnings for order ${orderId}: $${commission.toFixed(2)});

        // Create or update earnings record
        const earningsQuery = await db.collection('earnings')
          .where('orderId', '==', orderId)
          .where('userId', '==', userId)
          .get();

        if (earningsQuery.empty) {
          // Create earnings record
          await db.collection('earnings').add({
            userId: userId,
            orderId: orderId,
            amount: commission,
            status: 'confirmed',
            orderStatus: 'delivered',
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            confirmedAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          console.log( Created earnings record for order ${orderId});
        }

        // Mark order earnings as confirmed
        await orderDoc.ref.update({
          earningsConfirmed: true,
          earningsConfirmedAt: admin.firestore.FieldValue.serverTimestamp(),
          updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        });

        processedCount++;

      } catch (error) {
        console.error( Error processing order ${orderDoc.id}:, error);
        errorCount++;
      }
    }

    console.log(' Order Earnings Sync Summary:');
    console.log(    Processed: ${processedCount} orders);
    console.log(    Skipped: ${skippedCount} orders);
    console.log(    Errors: ${errorCount} orders);
    console.log(' Order earnings synchronization completed!');

  } catch (error) {
    console.error(' Error during order earnings sync:', error);
  }
}

// Main execution
async function main() {
  console.log(' Starting complete balance synchronization...');
  console.log('=====================================');
  
  await syncUserBalances();
  console.log('');
  await syncOrderEarnings();
  
  console.log('');
  console.log(' Complete synchronization finished!');
  console.log('Your admin panel and Flutter app should now have consistent balance data.');
  
  process.exit(0);
}

// Run the migration
main().catch(console.error);{expectedIncoming});
      console.log(   Actual: Available=// Migration Script: Sync User Balances Between Admin Panel and Flutter App
// This script ensures both collections have consistent data

const admin = require('firebase-admin');

// Initialize Firebase Admin (if not already initialized)
if (!admin.apps.length) {
  const serviceAccount = require('./dropshippingdinal-vq5iag-firebase-adminsdk-fbsvc-c5f412b27c.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    databaseURL: 'https://dropshippingdinal-vq5iag-default-rtdb.firebaseio.com'
  });
}

const db = admin.firestore();

async function syncUserBalances() {
  console.log(' Starting user balance synchronization...');
  
  try {
    // Get all users from the users collection (admin panel format)
    const usersSnapshot = await db.collection('users').get();
    console.log( Found ${usersSnapshot.size} users in admin panel collection);

    let syncedCount = 0;
    let createdCount = 0;
    let errorCount = 0;

    for (const userDoc of usersSnapshot.docs) {
      try {
        const userId = userDoc.id;
        const userData = userDoc.data();
        
        console.log( Processing user: ${userId});

        // Get current balance data from admin panel
        const currentBalance = userData.currentBalance || 0;
        const totalEarnings = userData.totalEarnings || 0;
        const pendingWithdrawals = userData.pendingWithdrawals || 0;

        // Check if user has userBalances record (Flutter app format)
        const userBalanceRef = db.collection('userBalances').doc(userId);
        const userBalanceDoc = await userBalanceRef.get();

        if (userBalanceDoc.exists) {
          // Update existing userBalances record
          const existingBalance = userBalanceDoc.data();
          
          await userBalanceRef.update({
            availableBalance: currentBalance,
            totalEarnings: totalEarnings,
            pendingWithdrawals: pendingWithdrawals,
            incomingEarnings: existingBalance.incomingEarnings || 0, // Keep existing incoming
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            syncedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          
          console.log( Updated userBalances for ${userId}: available=$${currentBalance});
          syncedCount++;
        } else {
          // Create new userBalances record
          await userBalanceRef.set({
            userId: userId,
            availableBalance: currentBalance,
            incomingEarnings: 0, // Start with 0 incoming earnings
            totalEarnings: totalEarnings,
            pendingWithdrawals: pendingWithdrawals,
            totalWithdrawn: 0,
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            syncedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          
          console.log( Created userBalances for ${userId}: available=$${currentBalance});
          createdCount++;
        }

        // Also ensure users collection has all required fields
        await userDoc.ref.update({
          lastActivity: userData.lastActivity || admin.firestore.FieldValue.serverTimestamp(),
          syncedAt: admin.firestore.FieldValue.serverTimestamp(),
        });

      } catch (error) {
        console.error( Error processing user ${userDoc.id}:, error);
        errorCount++;
      }
    }

    console.log(' Synchronization Summary:');
    console.log(    Updated: ${syncedCount} users);
    console.log(    Created: ${createdCount} users);
    console.log(    Errors: ${errorCount} users);
    console.log(' User balance synchronization completed!');

  } catch (error) {
    console.error(' Error during synchronization:', error);
  }
}

async function syncOrderEarnings() {
  console.log(' Starting order earnings synchronization...');
  
  try {
    // Get all orders that are delivered but don't have earnings confirmed
    const ordersSnapshot = await db.collection('orders')
      .where('status', '==', 'Delivered')
      .get();
    
    console.log( Found ${ordersSnapshot.size} delivered orders);

    let processedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    for (const orderDoc of ordersSnapshot.docs) {
      try {
        const orderId = orderDoc.id;
        const orderData = orderDoc.data();
        const userId = orderData.userId;
        const totalAmount = orderData.totalAmount || 0;
        const earningsConfirmed = orderData.earningsConfirmed || false;

        if (!userId) {
          console.log( Skipping order ${orderId}: no userId);
          skippedCount++;
          continue;
        }

        if (earningsConfirmed) {
          console.log( Skipping order ${orderId}: earnings already confirmed);
          skippedCount++;
          continue;
        }

        // Calculate commission (10%)
        const commission = totalAmount * 0.10;

        if (commission <= 0) {
          console.log( Skipping order ${orderId}: no commission to process);
          skippedCount++;
          continue;
        }

        console.log( Processing earnings for order ${orderId}: $${commission.toFixed(2)});

        // Create or update earnings record
        const earningsQuery = await db.collection('earnings')
          .where('orderId', '==', orderId)
          .where('userId', '==', userId)
          .get();

        if (earningsQuery.empty) {
          // Create earnings record
          await db.collection('earnings').add({
            userId: userId,
            orderId: orderId,
            amount: commission,
            status: 'confirmed',
            orderStatus: 'delivered',
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            confirmedAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          console.log( Created earnings record for order ${orderId});
        }

        // Mark order earnings as confirmed
        await orderDoc.ref.update({
          earningsConfirmed: true,
          earningsConfirmedAt: admin.firestore.FieldValue.serverTimestamp(),
          updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        });

        processedCount++;

      } catch (error) {
        console.error( Error processing order ${orderDoc.id}:, error);
        errorCount++;
      }
    }

    console.log(' Order Earnings Sync Summary:');
    console.log(    Processed: ${processedCount} orders);
    console.log(    Skipped: ${skippedCount} orders);
    console.log(    Errors: ${errorCount} orders);
    console.log(' Order earnings synchronization completed!');

  } catch (error) {
    console.error(' Error during order earnings sync:', error);
  }
}

// Main execution
async function main() {
  console.log(' Starting complete balance synchronization...');
  console.log('=====================================');
  
  await syncUserBalances();
  console.log('');
  await syncOrderEarnings();
  
  console.log('');
  console.log(' Complete synchronization finished!');
  console.log('Your admin panel and Flutter app should now have consistent balance data.');
  
  process.exit(0);
}

// Run the migration
main().catch(console.error);{userBalance?.availableBalance}, Incoming=// Migration Script: Sync User Balances Between Admin Panel and Flutter App
// This script ensures both collections have consistent data

const admin = require('firebase-admin');

// Initialize Firebase Admin (if not already initialized)
if (!admin.apps.length) {
  const serviceAccount = require('./dropshippingdinal-vq5iag-firebase-adminsdk-fbsvc-c5f412b27c.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    databaseURL: 'https://dropshippingdinal-vq5iag-default-rtdb.firebaseio.com'
  });
}

const db = admin.firestore();

async function syncUserBalances() {
  console.log(' Starting user balance synchronization...');
  
  try {
    // Get all users from the users collection (admin panel format)
    const usersSnapshot = await db.collection('users').get();
    console.log( Found ${usersSnapshot.size} users in admin panel collection);

    let syncedCount = 0;
    let createdCount = 0;
    let errorCount = 0;

    for (const userDoc of usersSnapshot.docs) {
      try {
        const userId = userDoc.id;
        const userData = userDoc.data();
        
        console.log( Processing user: ${userId});

        // Get current balance data from admin panel
        const currentBalance = userData.currentBalance || 0;
        const totalEarnings = userData.totalEarnings || 0;
        const pendingWithdrawals = userData.pendingWithdrawals || 0;

        // Check if user has userBalances record (Flutter app format)
        const userBalanceRef = db.collection('userBalances').doc(userId);
        const userBalanceDoc = await userBalanceRef.get();

        if (userBalanceDoc.exists) {
          // Update existing userBalances record
          const existingBalance = userBalanceDoc.data();
          
          await userBalanceRef.update({
            availableBalance: currentBalance,
            totalEarnings: totalEarnings,
            pendingWithdrawals: pendingWithdrawals,
            incomingEarnings: existingBalance.incomingEarnings || 0, // Keep existing incoming
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            syncedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          
          console.log( Updated userBalances for ${userId}: available=$${currentBalance});
          syncedCount++;
        } else {
          // Create new userBalances record
          await userBalanceRef.set({
            userId: userId,
            availableBalance: currentBalance,
            incomingEarnings: 0, // Start with 0 incoming earnings
            totalEarnings: totalEarnings,
            pendingWithdrawals: pendingWithdrawals,
            totalWithdrawn: 0,
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            syncedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          
          console.log( Created userBalances for ${userId}: available=$${currentBalance});
          createdCount++;
        }

        // Also ensure users collection has all required fields
        await userDoc.ref.update({
          lastActivity: userData.lastActivity || admin.firestore.FieldValue.serverTimestamp(),
          syncedAt: admin.firestore.FieldValue.serverTimestamp(),
        });

      } catch (error) {
        console.error( Error processing user ${userDoc.id}:, error);
        errorCount++;
      }
    }

    console.log(' Synchronization Summary:');
    console.log(    Updated: ${syncedCount} users);
    console.log(    Created: ${createdCount} users);
    console.log(    Errors: ${errorCount} users);
    console.log(' User balance synchronization completed!');

  } catch (error) {
    console.error(' Error during synchronization:', error);
  }
}

async function syncOrderEarnings() {
  console.log(' Starting order earnings synchronization...');
  
  try {
    // Get all orders that are delivered but don't have earnings confirmed
    const ordersSnapshot = await db.collection('orders')
      .where('status', '==', 'Delivered')
      .get();
    
    console.log( Found ${ordersSnapshot.size} delivered orders);

    let processedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    for (const orderDoc of ordersSnapshot.docs) {
      try {
        const orderId = orderDoc.id;
        const orderData = orderDoc.data();
        const userId = orderData.userId;
        const totalAmount = orderData.totalAmount || 0;
        const earningsConfirmed = orderData.earningsConfirmed || false;

        if (!userId) {
          console.log( Skipping order ${orderId}: no userId);
          skippedCount++;
          continue;
        }

        if (earningsConfirmed) {
          console.log( Skipping order ${orderId}: earnings already confirmed);
          skippedCount++;
          continue;
        }

        // Calculate commission (10%)
        const commission = totalAmount * 0.10;

        if (commission <= 0) {
          console.log( Skipping order ${orderId}: no commission to process);
          skippedCount++;
          continue;
        }

        console.log( Processing earnings for order ${orderId}: $${commission.toFixed(2)});

        // Create or update earnings record
        const earningsQuery = await db.collection('earnings')
          .where('orderId', '==', orderId)
          .where('userId', '==', userId)
          .get();

        if (earningsQuery.empty) {
          // Create earnings record
          await db.collection('earnings').add({
            userId: userId,
            orderId: orderId,
            amount: commission,
            status: 'confirmed',
            orderStatus: 'delivered',
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            confirmedAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          console.log( Created earnings record for order ${orderId});
        }

        // Mark order earnings as confirmed
        await orderDoc.ref.update({
          earningsConfirmed: true,
          earningsConfirmedAt: admin.firestore.FieldValue.serverTimestamp(),
          updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        });

        processedCount++;

      } catch (error) {
        console.error( Error processing order ${orderDoc.id}:, error);
        errorCount++;
      }
    }

    console.log(' Order Earnings Sync Summary:');
    console.log(    Processed: ${processedCount} orders);
    console.log(    Skipped: ${skippedCount} orders);
    console.log(    Errors: ${errorCount} orders);
    console.log(' Order earnings synchronization completed!');

  } catch (error) {
    console.error(' Error during order earnings sync:', error);
  }
}

// Main execution
async function main() {
  console.log(' Starting complete balance synchronization...');
  console.log('=====================================');
  
  await syncUserBalances();
  console.log('');
  await syncOrderEarnings();
  
  console.log('');
  console.log(' Complete synchronization finished!');
  console.log('Your admin panel and Flutter app should now have consistent balance data.');
  
  process.exit(0);
}

// Run the migration
main().catch(console.error);{userBalance?.incomingEarnings});
      return false;
    }
  } catch (error) {
    console.error(' Error checking balances:', error);
    return false;
  }
}

async function cleanupTestData() {
  console.log(' Cleaning up test data...');
  
  try {
    // Delete test user from both collections
    await db.collection('users').doc(TEST_USER_ID).delete();
    await db.collection('userBalances').doc(TEST_USER_ID).delete();

    // Delete test orders
    const ordersSnapshot = await db.collection('orders')
      .where('userId', '==', TEST_USER_ID)
      .get();
    
    for (const orderDoc of ordersSnapshot.docs) {
      await orderDoc.ref.delete();
    }

    // Delete test earnings
    const earningsSnapshot = await db.collection('earnings')
      .where('userId', '==', TEST_USER_ID)
      .get();
    
    for (const earningsDoc of earningsSnapshot.docs) {
      await earningsDoc.ref.delete();
    }

    // Delete test transactions
    const transactionsSnapshot = await db.collection('transactions')
      .where('userId', '==', TEST_USER_ID)
      .get();
    
    for (const transactionDoc of transactionsSnapshot.docs) {
      await transactionDoc.ref.delete();
    }

    console.log(' Test data cleaned up successfully');
  } catch (error) {
    console.error(' Error cleaning up test data:', error);
  }
}

// Main test execution
async function runCompleteEarningsFlowTest() {
  console.log(' Starting Complete Earnings Flow Test');
  console.log('==========================================');
  
  let testPassed = false;
  
  try {
    // Step 1: Create test user
    const userCreated = await createTestUser();
    if (!userCreated) {
      console.log(' Test failed: Could not create test user');
      return;
    }

    // Step 2: Create test order with incoming earnings
    const orderId = await createTestOrder();
    if (!orderId) {
      console.log(' Test failed: Could not create test order');
      return;
    }

    // Wait a moment for data to settle
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Step 3: Simulate admin marking order as delivered
    const deliveryProcessed = await simulateAdminDelivery(orderId);
    if (!deliveryProcessed) {
      console.log(' Test failed: Could not process delivery');
      return;
    }

    // Wait a moment for earnings processing
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Step 4: Check if earnings moved correctly
    testPassed = await checkBalances();

  } catch (error) {
    console.error(' Test execution error:', error);
  } finally {
    // Step 5: Clean up test data
    await cleanupTestData();
  }

  console.log('');
  console.log('==========================================');
  if (testPassed) {
    console.log(' COMPLETE EARNINGS FLOW TEST PASSED!');
    console.log(' Your earnings system is working correctly');
    console.log(' Orders marked as delivered will now show earnings in Flutter withdrawal page');
  } else {
    console.log(' COMPLETE EARNINGS FLOW TEST FAILED!');
    console.log(' There may be issues with the earnings flow system');
    console.log(' Check the logs above for specific error details');
  }
  
  process.exit(testPassed ? 0 : 1);
}

// Run the test
runCompleteEarningsFlowTest().catch(console.error);
